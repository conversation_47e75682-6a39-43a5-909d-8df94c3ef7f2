// components/modules/ModuleActivityList.tsx
import { ModuleActivityCard } from "./ModuleActivityCard"

export interface ActivityData {
  emoji: string
  title: string
  duration: string
  description: string
}

export function ModuleActivityList({
  activities,
}: {
  activities: ActivityData[]
}) {
  return (
    <section className="mt-6 space-y-4">
      <h2 className="text-lg font-semibold text-gray-800">
        Activities in this Module
      </h2>
      <div className="space-y-4">
        {activities.map((act, idx) => (
          <ModuleActivityCard key={idx} {...act} />
        ))}
      </div>
    </section>
  )
}
