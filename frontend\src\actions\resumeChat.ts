"use server"

import { revalidate<PERSON>ath } from "next/cache"
import api from "@/services/apiService"
import { BASE_API_URL } from "@/config/apiEndpoints"
import { UserProfile } from "@/types/user-profile"
import { ResumeData } from "@/types/resume-content"

/**
 * Send a chat message to the resume assistant
 * @param userId - The user ID
 * @param message - The message to send
 * @returns The response from the assistant
 */
export async function sendChatMessage(userId: string, message: string) {
  try {
    const response = await fetch(`${BASE_API_URL}/resume-editor/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: userId,
        message: message
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to send chat message');
    }

    const data = await response.json();
    return {
      success: true,
      response: data.response
    };
  } catch (error) {
    console.error('Error sending chat message:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

/**
 * Update resume data from chat suggestions
 * @param userId - The user ID
 * @param resumeId - The resume ID
 * @param updates - The updates to apply to the resume
 * @returns The result of the update operation
 */
export async function updateResumeFromChat(userId: string, resumeId: string, updates: Partial<ResumeData>) {
  try {
    const response = await fetch(`${BASE_API_URL}/resume-editor/resume`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: userId,
        resume_data: updates,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update resume');
    }

    const data = await response.json();
    // Revalidate the resume builder page to reflect the changes
    revalidatePath(`/app/resumeBuilder/${resumeId}`);
    
    return {
      success: true,
      data: data
    };
  } catch (error) {
    console.error('Error updating resume from chat:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

/**
 * Improve text content using AI
 * @param text - The text to improve
 * @param section - The section the text belongs to (e.g., 'experience', 'education', 'summary')
 * @returns The improved text
 */
export async function improveTextContent(text: string, section: string) {
  try {
    const response = await fetch(`${BASE_API_URL}/resume-editor/resume/improve-text`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text,
        section
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to improve text');
    }

    const data = await response.json();
    return {
      success: true,
      improvedText: data.improved_text
    };
  } catch (error) {
    console.error('Error improving text:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

/**
 * Analyze a specific resume section
 * @param userId - The user ID
 * @param section - The section to analyze
 * @param question - Optional specific question about the section
 * @returns Analysis of the section
 */
export async function analyzeResumeSection(userId: string, section: string, question?: string) {
  try {
    const response = await fetch(`${BASE_API_URL}/resume-editor/resume/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: userId,
        section: section,
        question: question
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to analyze resume section');
    }

    const data = await response.json();
    return {
      success: true,
      analysis: data.response
    };
  } catch (error) {
    console.error('Error analyzing resume section:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
