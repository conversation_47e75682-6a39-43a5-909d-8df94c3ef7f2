import { useState } from "react"
import { <PERSON><PERSON>ircle, Download, FileText, <PERSON>rk<PERSON>, Target } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

interface ChatMessage {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
}

interface GuidanceDoc {
  id: string
  title: string
  content: string
  keyPoints: string[]
  actionItems: string[]
  createdAt: Date
}

interface GuidanceGeneratorProps {
  sessionId: string
  messages: ChatMessage[]
  coachName: string
}

// Mock data generator
const generateMockGuidanceDoc = (messages: ChatMessage[]): GuidanceDoc => {
  // Extract topic from messages
  const userMessages = messages.filter((m) => m.role === "user")
  const lastUserMessage = userMessages[userMessages.length - 1]?.content || ""

  // Mock guidance based on common career topics
  const mockGuidance = {
    career: {
      title: "Career Transition Strategy Guide",
      content:
        "Based on our discussion about your career transition goals, I've prepared a comprehensive strategy guide. This document outlines key steps, potential challenges, and actionable recommendations to help you successfully navigate your career change.",
      keyPoints: [
        "Leverage your transferable skills from your current role",
        "Focus on upskilling in your target industry",
        "Build a network in your desired field",
        "Create a compelling career transition story",
      ],
      actionItems: [
        "Update your resume to highlight relevant transferable skills",
        "Join 2-3 professional associations in your target industry",
        "Complete an online course in your target field within 3 months",
        "Reach out to 3 professionals in your desired role for informational interviews",
      ],
    },
    resume: {
      title: "Resume Optimization Action Plan",
      content:
        "Following our resume review session, I've compiled key recommendations to enhance your resume's impact and improve your chances of getting interviews. This guide focuses on both ATS optimization and human reader engagement.",
      keyPoints: [
        "Optimize for ATS with industry-specific keywords",
        "Quantify achievements with metrics",
        "Improve visual hierarchy and readability",
        "Strengthen your professional summary",
      ],
      actionItems: [
        "Incorporate 5-7 key industry keywords throughout your resume",
        "Add metrics to at least 3 achievement statements",
        "Restructure your experience section using the STAR method",
        "Create a master resume document with all your experiences",
      ],
    },
    interview: {
      title: "Interview Preparation Framework",
      content:
        "Based on our mock interview session, I've created a structured framework to help you prepare for future interviews. This guide includes strategies for common questions and techniques to showcase your strengths effectively.",
      keyPoints: [
        "Master the STAR method for behavioral questions",
        "Research company culture and values",
        "Prepare strategic questions for interviewers",
        "Develop your unique value proposition",
      ],
      actionItems: [
        "Practice 10 common behavioral questions using the STAR method",
        "Research your target company's recent news and developments",
        "Prepare 5 strategic questions for interviewers",
        "Record yourself answering interview questions and review",
      ],
    },
  }

  // Select guidance type based on message content
  let guidanceType = "career"
  if (lastUserMessage.toLowerCase().includes("resume")) {
    guidanceType = "resume"
  } else if (lastUserMessage.toLowerCase().includes("interview")) {
    guidanceType = "interview"
  }

  const guidance = mockGuidance[guidanceType as keyof typeof mockGuidance]

  return {
    id: `guid-${Date.now()}`,
    title: guidance.title,
    content: guidance.content,
    keyPoints: guidance.keyPoints,
    actionItems: guidance.actionItems,
    createdAt: new Date(),
  }
}

export default function GuidanceGenerator({
  sessionId,
  messages,
  coachName,
}: GuidanceGeneratorProps) {
  const [guidanceDoc, setGuidanceDoc] = useState<GuidanceDoc | null>(null)
  const [generating, setGenerating] = useState(false)

  const handleGenerateGuidance = async () => {
    try {
      setGenerating(true)
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 2000))
      const doc = generateMockGuidanceDoc(messages)
      setGuidanceDoc(doc)
    } catch (error) {
      console.error("Failed to generate guidance doc:", error)
    } finally {
      setGenerating(false)
    }
  }

  const handleDownloadDoc = () => {
    if (!guidanceDoc) return

    const content = `
# ${guidanceDoc.title}
Generated by ${coachName} on ${guidanceDoc.createdAt.toLocaleDateString()}

## Summary
${guidanceDoc.content}

## Key Points
${guidanceDoc.keyPoints.map((point) => `• ${point}`).join("\n")}

## Action Items
${guidanceDoc.actionItems.map((item, index) => `${index + 1}. ${item}`).join("\n")}

---
Generated by Career Coach AI
    `.trim()

    const blob = new Blob([content], { type: "text/markdown" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `career-guidance-${Date.now()}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (!messages.length) {
    return (
      <Card className="border-dashed">
        <CardContent className="flex flex-col items-center justify-center py-8 text-center">
          <FileText className="mb-4 h-12 w-12 text-gray-400" />
          <p className="mb-2 text-gray-500">No conversation yet</p>
          <p className="text-sm text-gray-400">
            Start chatting to generate personalized guidance
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {!guidanceDoc ? (
        <Card className="border-blue-200 bg-blue-50/50">
          <CardHeader>
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-blue-900">
                Generate Guidance Document
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-blue-800">
              Create a personalized career guidance document based on your
              conversation with {coachName}.
            </p>
            <Button
              onClick={handleGenerateGuidance}
              disabled={generating}
              className="w-full"
            >
              {generating ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                  Generating Guidance...
                </>
              ) : (
                <>
                  <FileText className="mr-2 h-4 w-4" />
                  Generate Guidance Document
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card className="border-green-200 bg-green-50/50">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <CardTitle className="text-green-900">
                  {guidanceDoc.title}
                </CardTitle>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownloadDoc}
                className="border-green-300 text-green-700 hover:bg-green-100"
              >
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-green-800">{guidanceDoc.content}</p>

            <Separator />

            <div>
              <h4 className="mb-3 flex items-center gap-2 font-semibold text-green-900">
                <CheckCircle className="h-4 w-4" />
                Key Points
              </h4>
              <ul className="space-y-2">
                {guidanceDoc.keyPoints.map((point, index) => (
                  <li
                    key={index}
                    className="flex items-start gap-2 text-sm text-green-800"
                  >
                    <div className="mt-2 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-green-500"></div>
                    {point}
                  </li>
                ))}
              </ul>
            </div>

            <Separator />

            <div>
              <h4 className="mb-3 flex items-center gap-2 font-semibold text-green-900">
                <Target className="h-4 w-4" />
                Action Items
              </h4>
              <ol className="space-y-2">
                {guidanceDoc.actionItems.map((item, index) => (
                  <li
                    key={index}
                    className="flex items-start gap-3 text-sm text-green-800"
                  >
                    <span className="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-green-100 text-xs font-medium text-green-700">
                      {index + 1}
                    </span>
                    {item}
                  </li>
                ))}
              </ol>
            </div>

            <div className="border-t border-green-200 pt-2 text-xs text-green-600">
              Generated on {guidanceDoc.createdAt.toLocaleDateString()} by{" "}
              {coachName}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
