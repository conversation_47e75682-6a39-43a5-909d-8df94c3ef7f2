"use client"

import * as React from "react"
import Image from "next/image"

import { features } from "@/data/features"

import { cn } from "@/lib/utils"

export function FeaturesSection() {
  const [activeFeature, setActiveFeature] = React.useState(features[0])

  return (
    <section id="features-section" className="w-full bg-muted/20 py-24">
      <div className="container grid grid-cols-1 gap-12 md:grid-cols-12 md:items-start">
        {/* Left Sidebar: Feature Selectors */}
        <div className="md:col-span-4">
          <h2 className="mb-6 text-3xl font-bold">What SophieAI can do</h2>
          <ul className="space-y-2">
            {features.map((feature) => (
              <li key={feature.title}>
                <button
                  onClick={() => setActiveFeature(feature)}
                  className={cn(
                    "w-full rounded-lg border px-4 py-3 text-left transition",
                    activeFeature.title === feature.title
                      ? "border-black bg-white font-semibold shadow-sm"
                      : "bg-muted text-muted-foreground hover:bg-white"
                  )}
                >
                  {feature.title}
                </button>
              </li>
            ))}
          </ul>
        </div>

        {/* Right: Feature Preview */}
        <div className="mt-6 md:col-span-8 md:mt-0">
          <div className="mb-4">
            <h3 className="text-2xl font-semibold">{activeFeature.title}</h3>
            <p className="mt-2 text-muted-foreground">
              {activeFeature.description}
            </p>
          </div>

          <div className="relative h-[400px] w-full overflow-hidden rounded-xl border shadow-xl">
            <Image
              src={activeFeature.image}
              alt={activeFeature.title}
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
