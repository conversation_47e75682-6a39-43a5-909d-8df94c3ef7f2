import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from langchain_core.documents import Document
from loguru import logger

from career_coaches.config import settings
from common.application.rag.retrievers import Retriever, get_retriever
from common.application.rag.splitters import Splitter, get_splitter
from common.infrastructure.mongo.client import MongoClientWrapper 
from common.infrastructure.mongo.indexes import MongoIndex


class UserMemoryDocument(Document):
    """Extended Document class for user-specific memory storage."""

    def __init__(self, page_content: str, metadata: Dict[str, Any] = None, **kwargs):
        if metadata is None:
            metadata = {}

        # Ensure required metadata fields for user-specific memory
        metadata.setdefault("user_id", "")
        metadata.setdefault("coach_id", "")
        metadata.setdefault("memory_type", "general")  # general, conversation, goal, achievement, preference
        metadata.setdefault("timestamp", datetime.utcnow().isoformat())
        metadata.setdefault("importance", 1.0)  # 0.0 to 1.0 importance score

        super().__init__(page_content=page_content, metadata=metadata, **kwargs)


class CareerCoachLongTermMemoryCreator:
    """Creates and manages long-term memory for career coaches with user-specific support."""

    def __init__(self, retriever: Retriever, splitter: Splitter) -> None:
        self.retriever = retriever
        self.splitter = splitter

    @classmethod
    def build_from_settings(cls) -> "CareerCoachLongTermMemoryCreator":
        """Build the memory creator from configuration settings."""
        retriever = get_retriever(
            embedding_model_id=settings.RAG_TEXT_EMBEDDING_MODEL_ID,
            mongo_uri=settings.MONGO_URI,
            db_name=settings.MONGO_DB_NAME,
            collection_name=settings.MONGO_CAREER_LONG_TERM_MEMORY_COLLECTION,
            k=settings.RAG_TOP_K,
            device=settings.RAG_DEVICE,
        )
        splitter = get_splitter(chunk_size=settings.RAG_CHUNK_SIZE)

        return cls(retriever, splitter)

    def create_memory_from_documents(self, documents: list[Document]) -> None:
        """Create long-term memory from a list of documents.

        Args:
            documents: List of documents to process and store
        """
        if len(documents) == 0:
            logger.warning("No documents to process for career coach memory. Exiting.")
            return

        # First clear the long term memory collection to avoid duplicates.
        with MongoClientWrapper(
            model=Document,
            collection_name=settings.MONGO_CAREER_LONG_TERM_MEMORY_COLLECTION,
            database_name=settings.MONGO_DB_NAME,
            mongodb_uri=settings.MONGO_URI,
            app_name="career_coaches",
        ) as client:
            client.clear_collection()

        # Process documents
        chunked_docs = self.splitter.split_documents(documents)

        # Add documents to vector store
        self.retriever.vectorstore.add_documents(chunked_docs)

        # Create search index
        self.__create_index()

    def add_user_memory(
        self,
        user_id: str,
        coach_id: str,
        content: str,
        memory_type: str = "general",
        importance: float = 1.0,
        metadata: Dict[str, Any] = None
    ) -> None:
        """Add user-specific memory to the long-term memory store.

        Args:
            user_id: User identifier
            coach_id: Coach identifier
            content: Memory content to store
            memory_type: Type of memory (general, conversation, goal, achievement, preference)
            importance: Importance score (0.0 to 1.0)
            metadata: Additional metadata
        """
        if metadata is None:
            metadata = {}

        # Create user memory document
        memory_metadata = {
            "user_id": user_id,
            "coach_id": coach_id,
            "memory_type": memory_type,
            "timestamp": datetime.utcnow().isoformat(),
            "importance": importance,
            **metadata
        }

        user_doc = UserMemoryDocument(
            page_content=content,
            metadata=memory_metadata
        )

        # Split if content is too long
        chunked_docs = self.splitter.split_documents([user_doc])

        # Ensure all chunks maintain user metadata
        for chunk in chunked_docs:
            chunk.metadata.update(memory_metadata)

        # Add to vector store
        self.retriever.vectorstore.add_documents(chunked_docs)

        logger.info(f"Added {len(chunked_docs)} memory chunks for user {user_id} with coach {coach_id}")

    def add_conversation_memory(
        self,
        user_id: str,
        coach_id: str,
        conversation_summary: str,
        key_insights: List[str] = None,
        goals_discussed: List[str] = None
    ) -> None:
        """Add conversation summary to user's long-term memory.

        Args:
            user_id: User identifier
            coach_id: Coach identifier
            conversation_summary: Summary of the conversation
            key_insights: Key insights from the conversation
            goals_discussed: Goals discussed in the conversation
        """
        if key_insights is None:
            key_insights = []
        if goals_discussed is None:
            goals_discussed = []

        conversation_content = f"""
        Conversation Summary for {user_id}:
        Coach: {coach_id}
        Date: {datetime.utcnow().isoformat()}

        Summary: {conversation_summary}

        Key Insights:
        {chr(10).join(f"- {insight}" for insight in key_insights)}

        Goals Discussed:
        {chr(10).join(f"- {goal}" for goal in goals_discussed)}
        """

        self.add_user_memory(
            user_id=user_id,
            coach_id=coach_id,
            content=conversation_content,
            memory_type="conversation",
            importance=0.8,
            metadata={
                "key_insights": key_insights,
                "goals_discussed": goals_discussed
            }
        )

    def update_user_progress(
        self,
        user_id: str,
        coach_id: str,
        progress_data: Dict[str, Any]
    ) -> None:
        """Update user progress information in long-term memory.

        Args:
            user_id: User identifier
            coach_id: Coach identifier
            progress_data: Progress information to store
        """
        progress_content = f"""
        User Progress Update for {user_id}:
        Coach: {coach_id}
        Date: {datetime.utcnow().isoformat()}

        Progress Details:
        {json.dumps(progress_data, indent=2)}
        """

        self.add_user_memory(
            user_id=user_id,
            coach_id=coach_id,
            content=progress_content,
            memory_type="progress",
            importance=0.9,
            metadata={"progress_data": progress_data}
        )

    def __create_index(self) -> None:
        """Create the search index for the memory collection."""
        with MongoClientWrapper(
            model=Document, 
            collection_name=settings.MONGO_CAREER_LONG_TERM_MEMORY_COLLECTION,
            database_name=settings.MONGO_DB_NAME,
            mongodb_uri=settings.MONGO_URI,
            app_name="career_coaches",
        ) as client:
            self.index = MongoIndex(
                retriever=self.retriever,
                mongodb_client=client,
            )
            self.index.create(
                is_hybrid=True, embedding_dim=settings.RAG_TEXT_EMBEDDING_MODEL_DIM
            )


class CareerCoachLongTermMemoryRetriever:
    """Retrieves information from career coach long-term memory."""
    
    def __init__(self, retriever: Retriever) -> None:
        self.retriever = retriever

    @classmethod
    def build_from_settings(cls) -> "CareerCoachLongTermMemoryRetriever":
        """Build the memory retriever from configuration settings."""
        retriever = get_retriever(
            embedding_model_id=settings.RAG_TEXT_EMBEDDING_MODEL_ID,
            mongo_uri=settings.MONGO_URI,
            db_name=settings.MONGO_DB_NAME,
            collection_name=settings.MONGO_CAREER_LONG_TERM_MEMORY_COLLECTION,
            k=settings.RAG_TOP_K,
            device=settings.RAG_DEVICE,
        )

        return cls(retriever)

    def retrieve(self, query: str, user_id: str = None, coach_id: str = None, memory_type: str = None) -> list[Document]:
        """Retrieve relevant documents from long-term memory.

        Args:
            query: Search query
            user_id: Optional user ID for user-specific retrieval
            coach_id: Optional coach ID for coach-specific retrieval
            memory_type: Optional memory type filter

        Returns:
            List of relevant documents
        """
        # Get all relevant documents first
        all_docs = self.retriever.invoke(query)

        # Filter by user_id, coach_id, and memory_type if provided
        filtered_docs = []
        for doc in all_docs:
            metadata = doc.metadata

            # Apply filters
            if user_id and metadata.get("user_id") != user_id:
                continue
            if coach_id and metadata.get("coach_id") != coach_id:
                continue
            if memory_type and metadata.get("memory_type") != memory_type:
                continue

            filtered_docs.append(doc)

        logger.info(f"Retrieved {len(filtered_docs)} filtered documents from {len(all_docs)} total documents")
        return filtered_docs

    def get_user_memory_summary(self, user_id: str, coach_id: str = None) -> Dict[str, Any]:
        """Get a summary of user's memory across different types.

        Args:
            user_id: User identifier
            coach_id: Optional coach identifier

        Returns:
            Dictionary with memory summary by type
        """
        memory_types = ["general", "conversation", "goal", "achievement", "preference", "progress"]
        summary = {}

        for memory_type in memory_types:
            docs = self.retrieve("", user_id=user_id, coach_id=coach_id, memory_type=memory_type)
            summary[memory_type] = {
                "count": len(docs),
                "latest_timestamp": max([doc.metadata.get("timestamp", "") for doc in docs]) if docs else None,
                "average_importance": sum([doc.metadata.get("importance", 0.0) for doc in docs]) / len(docs) if docs else 0.0
            }

        return summary

    def get_recent_user_context(self, user_id: str, coach_id: str = None, limit: int = 5) -> str:
        """Get recent context about the user for coaching sessions.

        Args:
            user_id: User identifier
            coach_id: Optional coach identifier
            limit: Maximum number of recent memories to include

        Returns:
            Formatted context string
        """
        # Get recent memories sorted by importance and timestamp
        all_docs = self.retrieve("", user_id=user_id, coach_id=coach_id)

        # Enhanced sorting: combine recency and importance for better scoring
        def calculate_memory_score(doc):
            timestamp = doc.metadata.get("timestamp", "")
            importance = doc.metadata.get("importance", 0.0)
            memory_type = doc.metadata.get("memory_type", "general")

            # Simple recency scoring (more recent = higher score)
            try:
                from datetime import datetime
                doc_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                now = datetime.utcnow().replace(tzinfo=doc_time.tzinfo)
                days_old = (now - doc_time).days
                recency_score = max(0, 1.0 - (days_old / 30))  # Decay over 30 days
            except:
                recency_score = 0.5  # Default for invalid timestamps

            # Memory type importance boost
            type_boost = {
                "goal": 1.2, "achievement": 1.1, "preference": 1.1,
                "conversation": 1.0, "progress": 1.1, "general": 0.9
            }.get(memory_type, 1.0)

            return (importance * type_boost + recency_score) / 2

        # Sort by combined score
        sorted_docs = sorted(all_docs, key=calculate_memory_score, reverse=True)[:limit]

        if not sorted_docs:
            return f"No previous context found for user {user_id}"

        context_parts = [f"Recent context for user {user_id}:"]
        for doc in sorted_docs:
            memory_type = doc.metadata.get("memory_type", "general")
            timestamp = doc.metadata.get("timestamp", "")
            coach_source = doc.metadata.get("coach_id", "")
            score = calculate_memory_score(doc)

            context_parts.append(f"\n[{memory_type.upper()}|{coach_source}] (score: {score:.2f})")
            context_parts.append(doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content)

        return "\n".join(context_parts)
