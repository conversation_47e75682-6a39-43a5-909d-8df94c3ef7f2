"use client"

import { useState } from "react"
import { <PERSON><PERSON>2, <PERSON><PERSON><PERSON> } from "lucide-react"
import { toast } from "sonner"

import { scrapeJobUrl, JobScraperError } from "@/actions/jobScraperActions"

import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

import { JobDescriptionInput } from "./JobDescriptionInput"
import { ResumeSelect } from "./ResumeSelect"

// Helper to extract company name from URL
const extractCompanyFromUrl = (url: string): string => {
  try {
    const hostname = new URL(url).hostname
    const parts = hostname.split('.')
    
    // Remove common TLDs and subdomains
    const tlds = ['com', 'org', 'net', 'io', 'co', 'gov']
    const commonSubdomains = ['www', 'careers', 'jobs', 'apply']
    
    const filteredParts = parts.filter(part => 
      !tlds.includes(part) && !commonSubdomains.includes(part))
    
    if (filteredParts.length > 0) {
      // Get the most likely part to be the company name (usually the domain)
      return filteredParts[0].charAt(0).toUpperCase() + filteredParts[0].slice(1)
    }
    return ''
  } catch {
    return ''
  }
}

// Define what we'll submit to the API
interface TailorRequestData {
  company: string
  jobDescription: string
  companyDescription: string
  selectedResume: string
  generateResume: boolean
  generateCoverLetter: boolean
}

interface TailorNewRequestModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data?: TailorRequestData) => void
}

export function TailorNewRequestModal({
  open,
  onOpenChange,
  onSubmit,
}: TailorNewRequestModalProps) {
  const [jobInputMethod, setJobInputMethod] = useState<"url" | "paste">("url")
  const [jobUrl, setJobUrl] = useState("")
  // Role field removed as requested
  const [company, setCompany] = useState("")
  const [selectedResume, setSelectedResume] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [parsedJobDescription, setParsedJobDescription] = useState("")
  const [parsedCompanyDescription, setParsedCompanyDescription] = useState("")
  const [parsingError, setParsingError] = useState<boolean | "third-party">(
    false
  )
  const [parsing, setParsing] = useState(false)
  const [generateResume, setGenerateResume] = useState(true)
  const [generateCoverLetter, setGenerateCoverLetter] = useState(false)

  const resetForm = () => {
    setJobInputMethod("url")
    setJobUrl("")
    setParsedJobDescription("")
    setParsedCompanyDescription("")
    // Role reset removed
    setCompany("")
    setSelectedResume("")
    setParsing(false)
    setParsingError(false)
    setIsGenerating(false)
    setGenerateResume(true)
    setGenerateCoverLetter(false)
  }

  const handleCloseModal = () => {
    onOpenChange(false)
    resetForm()
  }

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      handleCloseModal()
    }
    onOpenChange(open)
  }

  const fetchAndParseJobUrl = async () => {
    setParsing(true)
    setParsingError(false)
    setParsedJobDescription("")
    setParsedCompanyDescription("")

    if (!jobUrl.trim()) {
      setParsing(false)
      toast.error("Please enter a valid job URL")
      return
    }

    try {
      // Check for third-party job boards
      const urlLower = jobUrl.toLowerCase()
      if (
        urlLower.includes("linkedin.com")
      ) {
        throw { message: "third-party", code: "THIRD_PARTY_SITE" }
      }

      // Try to extract potential company name from URL
      const extractedCompany = extractCompanyFromUrl(jobUrl)
      if (extractedCompany && !company) {
        setCompany(extractedCompany)
      }

      // Call the job scraper API
      console.log("🔍 Calling job scraper API with URL:", jobUrl)
      const scrapedData = await scrapeJobUrl(jobUrl)
      console.log("📥 Received scraped data:", scrapedData)
      
      // Update with scraped data
      if (scrapedData.job_description) {
        console.log("📝 Setting job description:", scrapedData.job_description.substring(0, 50) + "...")
        setParsedJobDescription(scrapedData.job_description)
      } else {
        console.warn("⚠️ No job description found in API response")
      }
      
      if (scrapedData.company_description) {
        setParsedCompanyDescription(scrapedData.company_description)
      }
      
      // Set company name directly from API response
      if (scrapedData.company_name) {
        setCompany(scrapedData.company_name)
        console.log("Setting company name from API: ", scrapedData.company_name)
      }
      
    } catch (err) {
      console.error("Error scraping job URL:", err)
      
      // Handle different error types
      if (err.code === "THIRD_PARTY_SITE" || err.message === "third-party") {
        setParsingError("third-party")
        toast.error("We don't support scraping from third-party job boards. Please enter details manually.")
      } else if (err.code === "NOT_FOUND" || err.status === 404) {
        setParsingError(true)
        toast.error("No job data found at this URL. Please try another URL or enter details manually.")
      } else if (err.code === "RATE_LIMIT" || err.status === 429) {
        setParsingError(true)
        toast.error("Too many requests. Please try again in a few minutes.")
      } else {
        setParsingError(true)
        toast.error("Failed to parse job posting. Please try again or enter details manually.")
      }
    } finally {
      setParsing(false)
    }
  }

  const handleStartTailoring = async () => {
    if (!company || !parsedJobDescription || !selectedResume) {
      toast.error("Please fill out all required fields")
      return
    }

    if (!generateResume && !generateCoverLetter) {
      toast.error("Please select at least one document to generate")
      return
    }

    console.log("🚀 Starting tailoring process...")
    console.log("📝 Form Data:", {
      resumeId: selectedResume,
      company,
      jobDescription: parsedJobDescription,
      companyDescription: parsedCompanyDescription,
      jobUrl,
      shouldTailorResume: generateResume,
      generateCoverLetter,
    })

    setIsGenerating(true)
    try {
      console.log("📤 Calling create-tailor API...")

      // Create FormData object
      const formData = new FormData()
      formData.append("resumeId", selectedResume)
      formData.append("jobTitle", company)
      formData.append("jobDescription", parsedJobDescription)
      formData.append("companyDescription", parsedCompanyDescription || "")
      formData.append("jobUrl", jobUrl || "")
      formData.append("companyName", company)
      formData.append("shouldTailorResume", String(generateResume))
      formData.append("generateCoverLetter", String(generateCoverLetter))

      const response = await fetch("/api/create-tailor", {
        method: "POST",
        body: formData,
      })

      console.log("📥 Received response status:", response.status)

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`)
      }

      const result = await response.json()
      console.log("📥 Parsed response data:", result)

      if (!result.success) {
        throw new Error(result.error || "Failed to create application")
      }

      toast.success(result.data.message)
      console.log("✅ Success! Calling onSubmit and closing modal...")
      onSubmit({
        company,
        jobDescription: parsedJobDescription,
        companyDescription: parsedCompanyDescription,
        selectedResume,
        generateResume,
        generateCoverLetter,
      })
      handleCloseModal()
    } catch (error) {
      console.error("❌ Error in handleStartTailoring:", error)
      toast.error(
        error instanceof Error ? error.message : "Failed to create application"
      )
    } finally {
      console.log("🏁 Finishing up, setting isGenerating to false")
      setIsGenerating(false)
    }
  }

  const isGenerateDisabled =
    isGenerating ||
    !selectedResume ||
    !parsedJobDescription.trim() ||
    !company.trim() ||
    (!generateResume && !generateCoverLetter)

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Tailored Resume</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <ResumeSelect
            selectedResume={selectedResume}
            onResumeChange={setSelectedResume}
          />

          <JobDescriptionInput
            jobInputMethod={jobInputMethod}
            onJobInputMethodChange={setJobInputMethod}
            jobUrl={jobUrl}
            onJobUrlChange={setJobUrl}
            company={company}
            onCompanyChange={setCompany}
            parsedJobDescription={parsedJobDescription}
            onParsedJobDescriptionChange={setParsedJobDescription}
            parsedCompanyDescription={parsedCompanyDescription}
            onParsedCompanyDescriptionChange={setParsedCompanyDescription}
            parsing={parsing}
            parsingError={parsingError}
            onFetchAndParse={fetchAndParseJobUrl}
          />

          {jobInputMethod === "url" && (
            <div className="rounded-lg border border-amber-200 bg-amber-50 p-3 text-sm text-amber-800">
              <p>
                <strong>Note:</strong> Our system will attempt to extract job and company details directly 
                from the provided URL. For best results, use official company career pages.
                Third-party job boards like LinkedIn, Indeed, or Glassdoor are
                not supported for automatic parsing.
              </p>
            </div>
          )}

          {/* Document Selection */}
          <div className="space-y-4 rounded-lg border border-gray-200 bg-gray-50 p-4">
            <h3 className="font-medium text-gray-900">
              What would you like to generate?
            </h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="resume"
                  checked={generateResume}
                  onCheckedChange={(checked) =>
                    setGenerateResume(checked as boolean)
                  }
                />
                <label
                  htmlFor="resume"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Tailored Resume
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="coverLetter"
                  checked={generateCoverLetter}
                  onCheckedChange={(checked) =>
                    setGenerateCoverLetter(checked as boolean)
                  }
                />
                <label
                  htmlFor="coverLetter"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Cover Letter
                </label>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4 border-t pt-4">
            <Button variant="outline" onClick={handleCloseModal}>
              Cancel
            </Button>
            <Button
              onClick={handleStartTailoring}
              disabled={isGenerateDisabled}
              className="min-w-[150px]"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 size-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 size-4" />
                  Start Tailoring
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
