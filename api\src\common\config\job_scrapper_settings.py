from pydantic import Field
from .base_settings import BaseAgentSettings

class JobScrapperSettings(BaseAgentSettings):
    """Settings for the Job Scrapper module."""
    
    # Tavily API settings
    TAVILY_API_KEY: str = Field(..., description="API key for Tavily search")
    
    # LLM settings for processing job descriptions
    LLM_MODEL: str = "llama-3.1-8b-instant"
    LLM_TEMPERATURE: float = 0.0
    
    # Tavily extraction settings
    EXTRACT_DEPTH: str = "advanced"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

# Create an instance of the settings
job_scrapper_settings = JobScrapperSettings()
