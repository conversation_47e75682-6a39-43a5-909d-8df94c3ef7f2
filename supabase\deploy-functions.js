const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const edgeFunctionsDir = path.join(__dirname, '../frontend/src/lib/supabase/edge-functions');
const projectRef = process.env.SUPABASE_PROJECT_REF || 'your-project-ref'; // Replace with your Supabase project reference

// Get a list of all edge function directories
const functionDirs = fs.readdirSync(edgeFunctionsDir).filter(dir => {
  return fs.statSync(path.join(edgeFunctionsDir, dir)).isDirectory();
});

console.log(`Found ${functionDirs.length} edge functions to deploy:`);
functionDirs.forEach(dir => console.log(`- ${dir}`));

// Deploy each function
functionDirs.forEach(functionName => {
  try {
    console.log(`\nDeploying function: ${functionName}...`);
    
    // Copy the function to a temporary directory that matches Supabase's expected structure
    const tempDir = path.join(__dirname, 'temp', functionName);
    fs.mkdirSync(tempDir, { recursive: true });
    
    // Copy function files
    const sourceDir = path.join(edgeFunctionsDir, functionName);
    const files = fs.readdirSync(sourceDir);
    
    files.forEach(file => {
      fs.copyFileSync(
        path.join(sourceDir, file),
        path.join(tempDir, file)
      );
    });
    
    // Deploy using Supabase CLI
    execSync(`supabase functions deploy ${functionName} --project-ref ${projectRef}`, {
      cwd: __dirname,
      stdio: 'inherit'
    });
    
    console.log(`✅ Successfully deployed function: ${functionName}`);
  } catch (error) {
    console.error(`❌ Failed to deploy function: ${functionName}`);
    console.error(error);
  }
});

console.log('\nDeployment process completed!');

// Reminder about environment variables
console.log('\n⚠️ IMPORTANT: Don\'t forget to set your environment variables:');
console.log('supabase secrets set MATERIAL_GENERATOR_API_URL=<your-api-url> --project-ref ' + projectRef);
