import { ContactSection } from "@/components/sections/contact-section"
import { FAQSection } from "@/components/sections/faq-section"
import { FeaturesSection } from "@/components/sections/features-section"
import { HeroSection } from "@/components/sections/hero-section"
import { NewsletterSection } from "@/components/sections/newsletter-section"
import { TestimonialsSection } from "@/components/sections/testimonials-section"
import { UserPathsSection } from "@/components/sections/user-paths-section"

export default function LandingPage(): JSX.Element {
  return (
    <div className="grid w-full grid-cols-1 items-center justify-center gap-16 md:gap-32">
      <HeroSection />
      {/* <TechSection /> */}
      {/* <BenefitsSection /> */}
      <FeaturesSection />
      <UserPathsSection />
      {/* <NewsletterSection />
      <TestimonialsSection /> */}
      {/* <PricingSection /> */}
      <FAQSection />
      <ContactSection />
    </div>
  )
}
