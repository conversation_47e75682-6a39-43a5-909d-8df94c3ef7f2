-- Career Coach Sessions Migration
-- This migration ensures the coach_chat_sessions table supports the new routing structure

-- Create coach_chat_sessions table if it doesn't exist
CREATE TABLE IF NOT EXISTS coach_chat_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    coach_id TEXT NOT NULL,
    chat_session_id TEXT NOT NULL,
    title TEXT,
    messages JSONB NOT NULL DEFAULT '[]'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    
    -- Ensure unique sessions per user
    UNIQUE(user_id, chat_session_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_coach_chat_sessions_user_id ON coach_chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_coach_chat_sessions_coach_id ON coach_chat_sessions(coach_id);
CREATE INDEX IF NOT EXISTS idx_coach_chat_sessions_user_coach ON coach_chat_sessions(user_id, coach_id);
CREATE INDEX IF NOT EXISTS idx_coach_chat_sessions_session_id ON coach_chat_sessions(chat_session_id);
CREATE INDEX IF NOT EXISTS idx_coach_chat_sessions_created_at ON coach_chat_sessions(created_at DESC);

-- Enable RLS (Row Level Security)
ALTER TABLE coach_chat_sessions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only access their own sessions
CREATE POLICY "Users can view their own chat sessions" 
ON coach_chat_sessions 
FOR SELECT 
USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own chat sessions" 
ON coach_chat_sessions 
FOR INSERT 
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own chat sessions" 
ON coach_chat_sessions 
FOR UPDATE 
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete their own chat sessions" 
ON coach_chat_sessions 
FOR DELETE 
USING (user_id = auth.uid());

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_coach_chat_sessions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_coach_chat_sessions_updated_at_trigger ON coach_chat_sessions;
CREATE TRIGGER update_coach_chat_sessions_updated_at_trigger
    BEFORE UPDATE ON coach_chat_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_coach_chat_sessions_updated_at();

-- Add helpful comments
COMMENT ON TABLE coach_chat_sessions IS 'Stores chat sessions between users and career coaches';
COMMENT ON COLUMN coach_chat_sessions.chat_session_id IS 'Unique identifier for the chat session, used in URLs';
COMMENT ON COLUMN coach_chat_sessions.messages IS 'JSON array of chat messages with role, content, and timestamp';
COMMENT ON COLUMN coach_chat_sessions.title IS 'User-friendly title for the session, auto-generated if not provided';
