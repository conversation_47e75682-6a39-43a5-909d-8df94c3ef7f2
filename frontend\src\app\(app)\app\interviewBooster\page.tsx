"use client"

import { useRouter } from "next/navigation"

import { Card, CardContent } from "@/components/ui/card"
import { ActivityCard } from "@/components/careerCoach/ActivityCard"

export default function InterviewBoosterPage() {
  const router = useRouter()

  const modules = [
    {
      id: "application-strategy",
      title: "Application Volume & Strategy",
      description:
        "Learn how to strategically increase your application volume without burning out. Track your outreach and tailor your approach to boost interview rates.",
      duration: "15–25 min",
      imageSrc: "/images/coachAI.png",
      type: "Module",
      href: "/app/interviewBooster/application-strategy",
    },
    {
      id: "resume-effectiveness",
      title: "Resume Effectiveness",
      description:
        "Assess your resume's ability to catch recruiters' attention. Get suggestions on formatting, keyword alignment, and clarity.",
      duration: "10–20 min",
      imageSrc: "/images/coachAI.png",
      type: "Module",
      href: "/app/interviewBooster/resume-effectiveness",
    },
    {
      id: "linkedin-profile",
      title: "LinkedIn Profile",
      description:
        "Optimize your LinkedIn for maximum recruiter visibility. Improve your headline, summary, and activity to drive opportunities.",
      duration: "20–30 min",
      imageSrc: "/images/coachAI.png",
      type: "Module",
      href: "/app/interviewBooster/linkedin-profile",
    },
  ]

  const activities = [
    {
      id: "match-skills",
      title: "Match your skills to the right job title",
      description:
        "Learn about which roles, job titles, or positions that best match your interests, skills, and experience.",
      duration: "10-15 min",
      icon: "🎯",
    },
    {
      id: "identify-occupations",
      title: "Identify occupations for you",
      description:
        "Learn about occupations that align with your interests and what you are good at doing.",
      duration: "15-20 min",
      icon: "🔍",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="border-b bg-white p-6">
        <h1 className="text-2xl font-bold text-gray-900">
          Interview Rate Booster
        </h1>
        <p className="mt-1 text-gray-600">
          Increase your chances of landing interviews by improving key
          application signals.
        </p>
      </div>

      {/* Focus Areas Section */}
      <section className="p-6">
        <h2 className="mb-6 text-xl font-semibold text-gray-900">
          Focus Areas
        </h2>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {modules.map((module) => (
            <Card
              key={module.id}
              onClick={() => router.push(module.href)}
              className="cursor-pointer transition-shadow hover:shadow-lg"
            >
              <div className="relative flex h-48 items-center justify-center overflow-hidden rounded-t-lg bg-gradient-to-br from-purple-600 via-pink-500 to-orange-400">
                <img
                  src={module.imageSrc}
                  alt={module.title}
                  className="h-full w-full object-cover"
                />
              </div>
              <CardContent className="p-6">
                <div className="mb-3 flex items-center gap-2">
                  <span className="rounded bg-blue-500 px-3 py-1 text-sm font-medium text-white">
                    {module.type}
                  </span>
                  <span className="rounded bg-gray-100 px-3 py-1 text-sm text-gray-600">
                    {module.duration}
                  </span>
                </div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900">
                  {module.title}
                </h3>
                <p className="text-sm text-gray-600">{module.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Activities Section (aligned with padding) */}
      <section className="p-6">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            Interactive Activities
          </h2>
          <button className="text-sm font-medium text-teal-600 hover:text-teal-700">
            See all
          </button>
        </div>
        <div className="space-y-4">
          {activities.map((activity) => (
            <ActivityCard key={activity.id} activity={activity} />
          ))}
        </div>
      </section>
    </div>
  )
}
