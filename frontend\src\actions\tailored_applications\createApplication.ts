import { revalidatePath } from "next/cache"

import type { Company, Job } from "@/types/application"

import { createClient } from "@/lib/supabase/server"

interface CreateApplicationPayload {
  resumeId: string
  jobTitle: string
  jobDescription: string
  jobUrl?: string
  companyName: string
  companyDescription?: string
  shouldTailorResume?: boolean
  generateCoverLetter?: boolean
  requestId: string
}

// Updated to include processing status
export type DocumentStatus = "pending" | "processing" | "completed" | "failed"

// Application status types
export type ApplicationStatus = "Pending" | "In Progress" | "Partially Completed" | "Completed" | "Failed"

interface DocumentGenerationPayload {
  applicationId: string
  resumeId: string
  jobDescription: string
  generateCoverLetter: boolean
  shouldTailorResume: boolean
}

// This function has been replaced by Supabase Edge Function

export async function createApplication(payload: CreateApplicationPayload) {
  console.log("🏁 Starting createApplication...")
  const supabase = createClient()

  try {
    // 1. Authenticate user
    console.log("🔐 Authenticating user...")
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      console.error("❌ Authentication failed:", authError)
      throw new Error("User not authenticated")
    }
    console.log("✅ User authenticated:", user.id)

    // 2. Upsert company
    console.log("🏢 Checking if company exists:", payload.companyName)
    const { data: existingCompany, error: companyError } = await supabase
      .from("companies")
      .select("*")
      .eq("name", payload.companyName)
      .maybeSingle()

    if (companyError) {
      console.error("❌ Error checking company:", companyError)
      throw new Error(`Error checking company: ${companyError.message}`)
    }

    let company: Company
    if (!existingCompany) {
      console.log("📝 Creating new company...")
      const { data: newCompany, error: insertCompanyError } = await supabase
        .from("companies")
        .insert({
          name: payload.companyName
          // Remove the description field as it doesn't exist in the schema
        })
        .select("*")
        .single()

      if (insertCompanyError || !newCompany) {
        console.error("❌ Error creating company:", insertCompanyError)
        throw new Error(
          `Error inserting company: ${insertCompanyError?.message}`
        )
      }
      company = newCompany as Company
      console.log("✅ New company created:", company.id)
    } else {
      company = existingCompany as Company
      console.log("✅ Using existing company:", company.id)
    }

    // 3. Insert job
    console.log("💼 Creating job record...")
    const { data: job, error: jobError } = await supabase
      .from("jobs")
      .insert({
        company_id: company.id,
        job_title: payload.jobTitle,
        job_description: payload.jobDescription,
        job_url: payload.jobUrl,
      })
      .select("*")
      .single()

    if (jobError || !job) {
      console.error("❌ Error creating job:", jobError)
      throw new Error(`Error inserting job: ${jobError?.message}`)
    }
    console.log("✅ Job created:", job.id)

    // 4. Create initial application
    const initialStatus: ApplicationStatus =
      payload.shouldTailorResume || payload.generateCoverLetter
        ? "In Progress"
        : "Completed"

    console.log("📄 Creating application with status:", initialStatus)
    const { data: application, error: appError } = await supabase
      .from("applications")
      .insert({
        user_id: user.id,
        resume_id: payload.resumeId,
        job_id: job.id,
        company_id: company.id,
        status: initialStatus,
        applied_date: new Date().toISOString(),
      })
      .select(
        `
        *,
        job:jobs(*),
        company:companies(*)
      `
      )
      .single()

    if (appError || !application) {
      console.error("❌ Error creating application:", appError)
      throw new Error(`Error inserting application: ${appError?.message}`)
    }
    console.log("✅ Application created:", application.id)

    // 5. Create document entries in pending state
    if (payload.shouldTailorResume || payload.generateCoverLetter) {
      console.log("📝 Creating document entries...")
      const documentEntries = []
      
      if (payload.shouldTailorResume) {
        documentEntries.push({
          application_id: application.id,
          document_type: 'resume',
          status: 'pending',
          created_at: new Date().toISOString(),
          request_id: payload.requestId
        })
      }
      
      if (payload.generateCoverLetter) {
        documentEntries.push({
          application_id: application.id,
          document_type: 'cover_letter',
          status: 'pending',
          created_at: new Date().toISOString(),
          request_id: payload.requestId
        })
      }
      
      if (documentEntries.length > 0) {
        const { data: documents, error: docError } = await supabase
          .from("tailored_documents")
          .insert(documentEntries)
          .select("*")
        
        if (docError) {
          console.error("❌ Error creating document entries:", docError)
          // Continue despite error, as the application was still created
        } else {
          console.log("✅ Document entries created:", documents.length)
        }
      }

      // Note: Document generation will be handled by the edge function
      // so we don't need to check for success/failure here
    }

    // 6. Revalidate and return
    console.log("🔄 Revalidating page...")
    revalidatePath("/app/resumeTailor")

    console.log("✅ All done! Returning success response")
    return {
      success: true,
      data: {
        application: {
          ...application,
          job: job as Job,
          company: company as Company,
        },
        message:
          initialStatus === "Completed"
            ? "Application created successfully."
            : "Application created successfully. Document generation has been queued.",
      },
    }
  } catch (error) {
    console.error("❌ Error in createApplication:", error)
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "An unknown error occurred",
    }
  }
}
