"use client"

import { useRouter } from "next/navigation"
import { ArrowLef<PERSON>, Award, ChevronRight, Clock, Users } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function ModuleDetailPage() {
  const router = useRouter()

  const handleActivityClick = (activityId: string) => {
    console.log(`Starting activity: ${activityId}`)
    // This will navigate to the form page for the specific activity
  }

  const activities = [
    {
      id: "match-skills",
      title: "Match your skills to the right job title",
      description:
        "Learn about which roles, job titles, or positions that best match your interests, skills, and experience.",
      duration: "10-15 min",
      icon: "🎯",
      href: "/app/interviewBooster/application-strategy/match-skills",
    },
    {
      id: "identify-occupations",
      title: "Identify occupations for you",
      description:
        "Learn about occupations that align with your interests and what you are good at doing.",
      duration: "15-20 min",
      icon: "🔍",
      href: "/app/interviewBooster/application-strategy/identify-occupations",
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <header className="sticky top-0 z-10 border-b border-border/50 bg-white/90 backdrop-blur-sm">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="flex items-center space-x-2 transition-colors hover:bg-slate-100"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Modules</span>
            </Button>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Users className="h-4 w-4" />
                <span>Interactive Learning</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section - Full Width */}
      <div className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative grid gap-0 lg:grid-cols-2">
          <div className="flex flex-col justify-center space-y-6 px-6 py-12 lg:px-12 lg:py-16">
            <div className="inline-flex w-fit items-center space-x-2 rounded-full bg-white/20 px-4 py-2 text-sm">
              <Award className="h-4 w-4" />
              <span>Career Development</span>
            </div>
            <h1 className="text-4xl font-bold leading-tight lg:text-5xl">
              Application Strategy
            </h1>
            <p className="max-w-2xl text-lg leading-relaxed text-blue-100 lg:text-xl">
              Learn how to strategically increase your application volume
              without burning out. Track your outreach and tailor your approach
              to boost interview rates.
            </p>
            <div className="flex flex-wrap items-center gap-4 pt-4">
              <div className="flex items-center space-x-2 rounded-lg bg-white/20 px-4 py-3">
                <Clock className="h-5 w-5" />
                <span className="font-medium">
                  {activities.length} Activities
                </span>
              </div>
              <div className="flex items-center space-x-2 rounded-lg bg-white/20 px-4 py-3">
                <span className="font-medium">
                  {activities.reduce((total, activity) => {
                    const mins = parseInt(activity.duration.split("-")[0]) || 10
                    return total + mins
                  }, 0)}
                  + min total
                </span>
              </div>
            </div>
          </div>
          <div className="relative min-h-[400px] lg:min-h-[500px]">
            <img
              src="/images/coachAI.png"
              alt="Application Strategy"
              className="absolute inset-0 h-full w-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-blue-600/30"></div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-6 py-12 lg:px-12">
        <div className="mx-auto max-w-7xl">
          {/* Progress Section */}
          <Card className="mb-12 border-0 bg-white/80 shadow-lg backdrop-blur-sm">
            <CardContent className="p-8">
              <div className="grid gap-8 md:grid-cols-3">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-foreground">
                    Your Progress
                  </h3>
                  <div className="h-3 w-full rounded-full bg-gray-200">
                    <div className="h-3 w-0 rounded-full bg-gradient-to-r from-orange-400 to-orange-500 transition-all duration-300"></div>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    0 of {activities.length} completed
                  </span>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-orange-500">0%</div>
                  <div className="text-sm text-muted-foreground">
                    Module Completion
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-blue-500">0 min</div>
                  <div className="text-sm text-muted-foreground">
                    Time Invested
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Activities Grid */}
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold text-foreground">
                Interactive Activities
              </h2>
              <span className="rounded-full bg-slate-100 px-4 py-2 text-sm text-muted-foreground">
                Click to start
              </span>
            </div>

            <div className="grid gap-6 lg:grid-cols-2">
              {activities.map((activity, index) => (
                <Card
                  key={activity.id}
                  className="group cursor-pointer border-0 bg-white transition-all duration-300 hover:bg-gradient-to-br hover:from-white hover:to-blue-50/50 hover:shadow-xl"
                  onClick={() => router.push(activity.href)}
                >
                  <CardContent className="p-8">
                    <div className="flex items-start space-x-6">
                      <div className="flex-shrink-0 space-y-3">
                        <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-orange-100 to-orange-200 text-3xl transition-transform group-hover:scale-110">
                          {activity.icon}
                        </div>
                        <div className="flex w-16 justify-center">
                          <span className="rounded-full bg-slate-100 px-3 py-1 text-xs font-bold text-muted-foreground">
                            {index + 1}
                          </span>
                        </div>
                      </div>

                      <div className="flex-1 space-y-4">
                        <div className="flex items-start justify-between">
                          <h3 className="text-xl font-bold leading-tight text-foreground transition-colors group-hover:text-blue-600">
                            {activity.title}
                          </h3>
                          <ChevronRight className="ml-4 h-6 w-6 flex-shrink-0 text-muted-foreground transition-all group-hover:translate-x-1 group-hover:text-blue-600" />
                        </div>
                        <p className="text-base leading-relaxed text-muted-foreground">
                          {activity.description}
                        </p>

                        <div className="flex items-center justify-between pt-3">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <div className="h-2 w-2 rounded-full bg-green-400"></div>
                              <span>Interactive</span>
                            </div>
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <div className="h-2 w-2 rounded-full bg-blue-400"></div>
                              <span>Personalized</span>
                            </div>
                          </div>
                          <span className="rounded-full bg-gradient-to-r from-slate-100 to-slate-200 px-3 py-2 text-sm font-semibold text-muted-foreground">
                            {activity.duration}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <Card className="mt-16 border-0 bg-gradient-to-r from-orange-50 via-orange-100 to-yellow-50 shadow-lg">
            <CardContent className="space-y-6 p-12 text-center">
              <h3 className="text-2xl font-bold text-foreground">
                Ready to Transform Your Career?
              </h3>
              <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
                Each activity includes interactive questions tailored to your
                situation and generates a comprehensive report with actionable
                insights to accelerate your career growth.
              </p>
              <Button className="bg-orange-500 px-8 py-3 text-lg text-white hover:bg-orange-600">
                Start Your Journey Now
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
