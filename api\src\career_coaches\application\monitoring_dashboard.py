"""
Monitoring dashboard for career coach system.
"""

import json
from datetime import datetime, timedelta
from typing import Dict, Any, List
import click
from tabulate import tabulate
from loguru import logger

from career_coaches.application.monitoring import CareerCoachMonitor
from career_coaches.domain.coach_factory import CoachFactory


class MonitoringDashboard:
    """Dashboard for monitoring career coach performance and system health."""
    
    def __init__(self):
        self.monitor = CareerCoachMonitor()
        self.coach_factory = CoachFactory()
    
    def display_system_overview(self) -> None:
        """Display overall system health and statistics."""
        print("\n" + "="*60)
        print("🎯 CAREER COACH MONITORING DASHBOARD")
        print("="*60)
        
        # Active sessions
        active_sessions = len(self.monitor.active_sessions)
        print(f"📊 Active Sessions: {active_sessions}")
        
        if active_sessions > 0:
            print("\n🔄 Active Sessions:")
            session_data = []
            for session_id, session in self.monitor.active_sessions.items():
                duration = (datetime.utcnow() - datetime.fromisoformat(session["start_time"])).total_seconds() / 60
                session_data.append([
                    session_id[:12] + "...",
                    session["user_id"][:10] + "...",
                    session["coach_id"],
                    f"{duration:.1f}m",
                    session["message_count"]
                ])
            
            headers = ["Session ID", "User ID", "Coach", "Duration", "Messages"]
            print(tabulate(session_data, headers=headers, tablefmt="grid"))
    
    def display_coach_performance(self, days: int = 7) -> None:
        """Display performance metrics for all coaches."""
        print(f"\n📈 COACH PERFORMANCE (Last {days} days)")
        print("-" * 60)
        
        available_coaches = self.coach_factory.get_available_coaches()
        performance_data = []
        
        for coach_id in available_coaches:
            try:
                metrics = self.monitor.get_coach_performance_metrics(coach_id, days)
                if metrics and metrics.total_conversations > 0:
                    performance_data.append([
                        coach_id,
                        metrics.total_conversations,
                        metrics.total_users,
                        f"{metrics.average_conversation_duration:.1f}s",
                        f"{metrics.average_response_time:.1f}s",
                        f"{metrics.average_user_satisfaction:.1f}/5",
                        f"{metrics.average_coaching_effectiveness:.1f}/5",
                        f"{metrics.error_rate:.1%}"
                    ])
            except Exception as e:
                logger.warning(f"Could not get metrics for coach {coach_id}: {e}")
                performance_data.append([
                    coach_id, "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A"
                ])
        
        if performance_data:
            headers = [
                "Coach ID", "Conversations", "Users", "Avg Duration", 
                "Avg Response", "Satisfaction", "Effectiveness", "Error Rate"
            ]
            print(tabulate(performance_data, headers=headers, tablefmt="grid"))
        else:
            print("No performance data available for the specified period.")
    
    def display_detailed_coach_metrics(self, coach_id: str, days: int = 7) -> None:
        """Display detailed metrics for a specific coach."""
        print(f"\n🔍 DETAILED METRICS: {coach_id.upper()} (Last {days} days)")
        print("-" * 60)
        
        try:
            metrics = self.monitor.get_coach_performance_metrics(coach_id, days)
            if not metrics:
                print("❌ No metrics available for this coach.")
                return
            
            # Basic metrics
            print("📊 Basic Metrics:")
            basic_data = [
                ["Total Conversations", metrics.total_conversations],
                ["Unique Users", metrics.total_users],
                ["Average Duration", f"{metrics.average_conversation_duration:.1f} seconds"],
                ["Average Response Time", f"{metrics.average_response_time:.1f} seconds"],
                ["Memory Operations", metrics.memory_operations],
                ["Error Rate", f"{metrics.error_rate:.1%}"]
            ]
            print(tabulate(basic_data, headers=["Metric", "Value"], tablefmt="grid"))
            
            # Quality metrics
            print("\n⭐ Quality Metrics:")
            quality_data = [
                ["User Satisfaction", f"{metrics.average_user_satisfaction:.1f}/5.0"],
                ["Coaching Effectiveness", f"{metrics.average_coaching_effectiveness:.1f}/5.0"],
                ["Goals Set", metrics.total_goals_set],
                ["Achievements Recorded", metrics.total_achievements_recorded]
            ]
            print(tabulate(quality_data, headers=["Metric", "Value"], tablefmt="grid"))
            
            # Improvement suggestions
            if metrics.improvement_suggestions:
                print("\n💡 Improvement Suggestions:")
                for i, suggestion in enumerate(metrics.improvement_suggestions, 1):
                    print(f"   {i}. {suggestion}")
            
        except Exception as e:
            print(f"❌ Error retrieving metrics: {e}")
    
    def display_system_health(self) -> None:
        """Display system health indicators."""
        print("\n🏥 SYSTEM HEALTH")
        print("-" * 60)
        
        health_indicators = []
        
        # Check active sessions
        active_count = len(self.monitor.active_sessions)
        session_status = "🟢 Normal" if active_count < 10 else "🟡 High Load" if active_count < 20 else "🔴 Overloaded"
        health_indicators.append(["Active Sessions", active_count, session_status])
        
        # Check memory system
        try:
            from career_coaches.application.long_term_memory import CareerCoachLongTermMemoryRetriever
            retriever = CareerCoachLongTermMemoryRetriever.build_from_settings()
            memory_status = "🟢 Operational"
        except Exception:
            memory_status = "🔴 Error"
        health_indicators.append(["Long-term Memory", "N/A", memory_status])
        
        # Check database connectivity
        try:
            # This would check MongoDB connectivity
            db_status = "🟢 Connected"
        except Exception:
            db_status = "🔴 Disconnected"
        health_indicators.append(["Database", "N/A", db_status])
        
        headers = ["Component", "Value", "Status"]
        print(tabulate(health_indicators, headers=headers, tablefmt="grid"))
    
    def export_metrics_report(self, output_file: str, days: int = 7) -> None:
        """Export comprehensive metrics report to JSON file."""
        print(f"\n📄 Exporting metrics report to {output_file}...")
        
        report = {
            "generated_at": datetime.utcnow().isoformat(),
            "period_days": days,
            "active_sessions": len(self.monitor.active_sessions),
            "coaches": {}
        }
        
        available_coaches = self.coach_factory.get_available_coaches()
        
        for coach_id in available_coaches:
            try:
                metrics = self.monitor.get_coach_performance_metrics(coach_id, days)
                if metrics:
                    report["coaches"][coach_id] = {
                        "total_conversations": metrics.total_conversations,
                        "total_users": metrics.total_users,
                        "average_conversation_duration": metrics.average_conversation_duration,
                        "average_response_time": metrics.average_response_time,
                        "average_user_satisfaction": metrics.average_user_satisfaction,
                        "average_coaching_effectiveness": metrics.average_coaching_effectiveness,
                        "memory_operations": metrics.memory_operations,
                        "error_rate": metrics.error_rate,
                        "improvement_suggestions": metrics.improvement_suggestions
                    }
            except Exception as e:
                report["coaches"][coach_id] = {"error": str(e)}
        
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"✅ Report exported successfully!")


@click.command()
@click.option("--coach-id", help="Show detailed metrics for specific coach")
@click.option("--days", default=7, help="Number of days to analyze (default: 7)")
@click.option("--export", help="Export report to JSON file")
@click.option("--watch", is_flag=True, help="Watch mode - refresh every 30 seconds")
def main(coach_id: str = None, days: int = 7, export: str = None, watch: bool = False):
    """Career Coach Monitoring Dashboard CLI."""
    
    dashboard = MonitoringDashboard()
    
    def display_dashboard():
        # Clear screen (works on most terminals)
        print("\033[2J\033[H")
        
        dashboard.display_system_overview()
        dashboard.display_system_health()
        
        if coach_id:
            dashboard.display_detailed_coach_metrics(coach_id, days)
        else:
            dashboard.display_coach_performance(days)
        
        if export:
            dashboard.export_metrics_report(export, days)
        
        if watch:
            print(f"\n🔄 Refreshing every 30 seconds... (Press Ctrl+C to exit)")
    
    if watch:
        import time
        try:
            while True:
                display_dashboard()
                time.sleep(30)
        except KeyboardInterrupt:
            print("\n👋 Monitoring stopped.")
    else:
        display_dashboard()


if __name__ == "__main__":
    main()
