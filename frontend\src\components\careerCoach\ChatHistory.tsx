"use client"

import { useEffect, useState } from "react"
import { ChevronRight, Clock, MessageSquare } from "lucide-react"

import { Card, CardContent } from "@/components/ui/card"

interface Message {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
}

interface ChatSession {
  id: string
  name: string
  summary: string
  updatedAt: Date
  messages: Message[]
}

interface ChatHistoryProps {
  coachId: string
  onSelectSession: (session: ChatSession) => void
}

// Mock data generator
const generateMockSessions = (coachId: string): ChatSession[] => {
  const topics = [
    "Career Transition Planning",
    "Resume Review Session",
    "Interview Preparation",
    "Salary Negotiation Strategy",
    "LinkedIn Profile Optimization",
  ]

  const summaries = [
    "Discussed strategies for transitioning from tech to product management",
    "Reviewed resume and identified key improvements for ATS optimization",
    "Practiced behavioral interview questions and STAR method responses",
    "Created action plan for upcoming performance review and raise discussion",
    "Analyzed LinkedIn profile and outlined engagement strategy",
  ]

  const mockMessages = [
    "Can you help me transition into product management?",
    "What skills should I focus on developing?",
    "How can I showcase my transferable skills?",
    "What certifications would be valuable?",
    "How should I approach networking in this field?",
  ]

  return Array.from({ length: Math.floor(Math.random() * 3) + 2 }, (_, i) => ({
    id: `session-${coachId}-${i}`,
    name: topics[i % topics.length],
    summary: summaries[i % summaries.length],
    updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Random date within last week
    messages: Array.from(
      { length: Math.floor(Math.random() * 6) + 4 }, // 4-10 messages
      (_, j) => ({
        id: `msg-${i}-${j}`,
        content:
          j % 2 === 0
            ? mockMessages[j % mockMessages.length]
            : "Here's my professional advice based on your situation...",
        role: (j % 2 === 0 ? "user" : "assistant") as "user" | "assistant",
        timestamp: new Date(Date.now() - (10 - j) * 60000), // Messages spaced 1 minute apart
      })
    ),
  })).sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()) // Sort by most recent
}

export default function ChatHistory({
  coachId,
  onSelectSession,
}: ChatHistoryProps) {
  const [sessions, setSessions] = useState<ChatSession[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate API delay
    const timer = setTimeout(() => {
      setSessions(generateMockSessions(coachId))
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [coachId])

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return "Yesterday"
    if (diffDays < 7) return `${diffDays} days ago`
    return date.toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="space-y-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="h-16 rounded-lg bg-gray-200"></div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-3">
      {sessions.length === 0 ? (
        <div className="py-8 text-center">
          <MessageSquare className="mx-auto mb-2 h-8 w-8 text-gray-400" />
          <p className="mb-1 text-sm text-gray-500">No previous sessions</p>
          <p className="text-xs text-gray-400">Start chatting to see history</p>
        </div>
      ) : (
        <>
          {sessions.map((session) => (
            <Card
              key={session.id}
              className="cursor-pointer border-0 shadow-none transition-all hover:bg-gray-50"
              onClick={() => onSelectSession(session)}
            >
              <CardContent className="p-3">
                <div className="flex items-start justify-between">
                  <div className="min-w-0 flex-1">
                    <h4 className="mb-1 truncate text-sm font-medium text-gray-900">
                      {session.name}
                    </h4>
                    <p className="mb-2 line-clamp-2 text-xs text-gray-600">
                      {session.summary}
                    </p>
                    <div className="flex items-center gap-3 text-xs text-gray-500">
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatDate(session.updatedAt)}
                      </span>
                      <span className="flex items-center gap-1">
                        <MessageSquare className="h-3 w-3" />
                        {session.messages.length}
                      </span>
                    </div>
                  </div>
                  <ChevronRight className="ml-2 h-4 w-4 flex-shrink-0 text-gray-400" />
                </div>
              </CardContent>
            </Card>
          ))}
        </>
      )}
    </div>
  )
}
