import { Bot } from "lucide-react"

interface TypingIndicatorProps {
  coachName?: string
}

export default function TypingIndicator({ coachName }: TypingIndicatorProps) {
  return (
    <div className="flex justify-start mb-4">
      <div className="flex items-start gap-3 max-w-xs lg:max-w-md xl:max-w-lg">
        <div className="flex size-8 items-center justify-center rounded-full bg-gray-100 mt-1 shrink-0">
          <Bot className="size-4 text-gray-600" />
        </div>
        <div className="bg-white border border-gray-200 rounded-2xl rounded-tl-md px-4 py-3 shadow-sm">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-xs font-medium text-gray-700">
              {coachName || "Career Coach"}
            </span>
            <span className="text-xs text-gray-500">typing...</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="flex space-x-1">
              <div 
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "0ms" }}
              ></div>
              <div 
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "150ms" }}
              ></div>
              <div 
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "300ms" }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
