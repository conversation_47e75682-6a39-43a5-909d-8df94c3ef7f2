"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import {
  getCoachById,
  getChatSessions,
  createChatSession,
  type Coach,
  type ChatSession
} from "@/services/careerCoachService"

// Simple UUID generation for demo purposes
// In production, this should come from authentication
const generateUserId = () => {
  if (typeof window !== 'undefined') {
    let userId = localStorage.getItem('demo_user_id')
    if (!userId) {
      userId = crypto.randomUUID()
      localStorage.setItem('demo_user_id', userId)
    }
    return userId
  }
  return crypto.randomUUID()
}
import {
  ArrowLeft,
  MessageSquare,
  Plus,
  Clock,
  Trash2,
  Edit3,
  User,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"

// ChatSession interface is now imported from the service

export default function CoachHistoryPage() {
  const params = useParams()
  const router = useRouter()
  const coachId = params.coach_id as string

  const [coach, setCoach] = useState<Coach | null>(null)
  const [sessions, setSessions] = useState<ChatSession[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [newSessionTitle, setNewSessionTitle] = useState("")
  const [isCreatingSession, setIsCreatingSession] = useState(false)

  useEffect(() => {
    async function loadCoachAndSessions() {
      try {
        setLoading(true)
        
        // Load coach details
        const coachData = await getCoachById(coachId)
        if (!coachData) {
          setError("Coach not found")
          return
        }
        setCoach(coachData)

        // Load sessions for this coach
        const userId = generateUserId()
        const sessionsData = await getChatSessions(userId, coachId)
        setSessions(sessionsData)
        setError(null)
      } catch (err) {
        setError("Failed to load coach details and sessions")
        console.error("Error loading coach and sessions:", err)
      } finally {
        setLoading(false)
      }
    }

    if (coachId) {
      loadCoachAndSessions()
    }
  }, [coachId])

  const handleCreateSession = async () => {
    try {
      setIsCreatingSession(true)

      const userId = generateUserId()
      const result = await createChatSession(
        coachId,
        userId,
        newSessionTitle || "New Chat Session"
      )

      if (result) {
        router.push(`/app/careerCoach/${coachId}/${result.chat_session_id}`)
      } else {
        throw new Error("Failed to create session")
      }
    } catch (err) {
      console.error("Error creating session:", err)
      // For now, create a mock session
      const mockSessionId = `session-${Date.now()}`
      router.push(`/app/careerCoach/${coachId}/${mockSessionId}`)
    } finally {
      setIsCreatingSession(false)
      setNewSessionTitle("")
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return "Yesterday"
    if (diffDays < 7) return `${diffDays} days ago`
    return date.toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto mb-4 size-12 animate-spin rounded-full border-4 border-blue-100 border-t-blue-600"></div>
          <p className="text-lg text-gray-500">Loading your sessions...</p>
        </div>
      </div>
    )
  }

  if (error || !coach) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 p-6">
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <h2 className="text-xl font-semibold text-red-600">
              Something went wrong
            </h2>
            <p className="text-gray-600">{error || "Coach not found"}</p>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => router.push("/app/careerCoach")}
              className="w-full"
            >
              Return to Career Coach
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="border-b bg-white p-6 shadow-sm">
        <div className="mx-auto max-w-4xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push("/app/careerCoach")}
                className="text-gray-500 hover:text-gray-700"
              >
                <ArrowLeft className="mr-2 size-4" />
                Back to Coaches
              </Button>

              <div className="flex items-center gap-3">
                <div className="flex size-12 items-center justify-center rounded-full bg-blue-100">
                  <User className="size-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {coach.name}
                  </h1>
                  <p className="text-gray-600">{coach.specialty}</p>
                </div>
              </div>
            </div>

            <Dialog>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="mr-2 size-4" />
                  New Session
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Start New Session</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">
                      Session Title (Optional)
                    </label>
                    <Input
                      value={newSessionTitle}
                      onChange={(e) => setNewSessionTitle(e.target.value)}
                      placeholder="e.g., Resume Review, Interview Prep..."
                      className="mt-1"
                    />
                  </div>
                  <div className="flex gap-3">
                    <Button
                      onClick={handleCreateSession}
                      disabled={isCreatingSession}
                      className="flex-1"
                    >
                      {isCreatingSession ? "Creating..." : "Start Session"}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="mx-auto max-w-4xl p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            Your Sessions with {coach.name}
          </h2>
          <p className="text-gray-600">
            Continue previous conversations or start a new session
          </p>
        </div>

        {sessions.length === 0 ? (
          <Card className="text-center py-12">
            <CardContent>
              <MessageSquare className="mx-auto mb-4 size-12 text-gray-400" />
              <h3 className="mb-2 text-lg font-medium text-gray-900">
                No sessions yet
              </h3>
              <p className="mb-4 text-gray-600">
                Start your first conversation with {coach.name}
              </p>
              <Button onClick={handleCreateSession} disabled={isCreatingSession}>
                <Plus className="mr-2 size-4" />
                Start First Session
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {sessions.map((session) => (
              <Card
                key={session.id}
                className="cursor-pointer transition-all hover:shadow-md hover:border-blue-200"
                onClick={() =>
                  router.push(`/app/careerCoach/${coachId}/${session.chat_session_id}`)
                }
              >
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="mb-2 text-lg font-medium text-gray-900">
                        {session.title}
                      </h3>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Clock className="size-4" />
                          {formatDate(session.updated_at)}
                        </div>
                        {session.message_count && (
                          <div className="flex items-center gap-1">
                            <MessageSquare className="size-4" />
                            {session.message_count} messages
                          </div>
                        )}
                      </div>
                    </div>
                    <Badge variant="outline" className="ml-4">
                      Continue
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
