"use client"

import { useState } from "react"
import { <PERSON><PERSON>, User } from "lucide-react"

import { sendChatMessage, analyzeResumeSection, improveTextContent } from "@/actions/resumeChat"
import type { ResumeData } from "@/types/resume-content"

import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/components/ui/use-toast"

interface Message {
  id: string
  type: "user" | "assistant"
  content: string
  timestamp: Date
}

interface AiChatPanelProps {
  userId: string
  resumeId: string
  onSuggestion: (suggestion: Partial<ResumeData>) => void
  onCollapse?: () => void
}

export function AiChatPanel({ userId, resumeId, onSuggestion, onCollapse }: AiChatPanelProps) {
  const { toast } = useToast()
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      type: "assistant",
      content:
        "Hi, there! Welcome to Resume Copilot, where I'll assist you in creating an outstanding resume. How can I help you with your resume today?",
      timestamp: new Date(),
    },
  ])
  const [inputValue, setInputValue] = useState("")
  const [isTyping, setIsTyping] = useState(false)

  const quickActions = [
    "Improve my summary",
    "Check my experience section",
    "Analyze my skills",
  ]

  const handleSend = async () => {
    if (!inputValue.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInputValue("")
    setIsTyping(true)

    try {
      // Send message to backend API
      const response = await sendChatMessage(userId, inputValue.trim())
      
      if (response.success) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: "assistant",
          content: response.response,
          timestamp: new Date(),
        }
        
        setMessages((prev) => [...prev, aiMessage])
        
        // Note: For future implementation, the backend could return suggested updates
        // If we implement that feature, we would handle them here
      } else {
        throw new Error(response.error || "Failed to get a response")
      }
    } catch (error) {
      console.error("Error in chat:", error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: "Sorry, I encountered an error processing your request. Please try again later.",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setIsTyping(false)
    }
  }

  const handleQuickAction = async (action: string) => {
    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: action,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setIsTyping(true)

    try {
      let response = null;
      
      switch (action) {
        case "Improve my summary":
          // Get the current summary and improve it
          response = await improveTextContent("Your professional summary", "summary");
          if (response.success && onSuggestion) {
            onSuggestion({
              objective: response.response
            });
          }
          break;
          
        case "Check my experience section":
          // Analyze experience section
          response = await analyzeResumeSection(userId, "experience", "How can I improve my work experience descriptions?");
          break;
          
        case "Analyze my skills":
          // Analyze skills section
          response = await analyzeResumeSection(userId, "skills", "Are my skills well organized and relevant?");
          break;
          
        default:
          response = { success: true, response: "How can I assist you with your resume today?" };
      }

      if (response && response.success) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: "assistant",
          content: response.response,
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, aiMessage]);
      } else {
        throw new Error(response?.error || "Failed to process your request");
      }
    } catch (error) {
      console.error("Error handling quick action:", error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: "Sorry, I encountered an error processing your request. Please try again later.",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  }

  return (
    <div className="flex h-full flex-col bg-white">
      {/* Header */}
      <div className="border-b border-gray-200 bg-gray-50 p-4">
        <h2 className="text-lg font-semibold text-gray-900">Resume Copilot</h2>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${message.type === "user" ? "justify-end" : "justify-start"}`}
            >
              {message.type === "assistant" && (
                <Avatar className="mt-1 size-8">
                  <AvatarFallback className="bg-blue-100 text-blue-600">
                    <Bot className="size-4" />
                  </AvatarFallback>
                </Avatar>
              )}
              <div
                className={`max-w-[80%] rounded-2xl p-4 ${
                  message.type === "user"
                    ? "rounded-br-md bg-blue-600 text-white"
                    : "rounded-bl-md bg-gray-100 text-gray-900"
                }`}
              >
                <p className="text-sm leading-relaxed">{message.content}</p>
              </div>
              {message.type === "user" && (
                <Avatar className="mt-1 size-8">
                  <AvatarFallback className="bg-gray-100 text-gray-600">
                    <User className="size-4" />
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          ))}

          {isTyping && (
            <div className="flex justify-start gap-3">
              <Avatar className="mt-1 size-8">
                <AvatarFallback className="bg-blue-100 text-blue-600">
                  <Bot className="size-4" />
                </AvatarFallback>
              </Avatar>
              <div className="rounded-2xl rounded-bl-md bg-gray-100 p-4">
                <div className="flex space-x-1">
                  <div className="size-2 animate-bounce rounded-full bg-gray-400"></div>
                  <div
                    className="size-2 animate-bounce rounded-full bg-gray-400"
                    style={{ animationDelay: "0.1s" }}
                  ></div>
                  <div
                    className="size-2 animate-bounce rounded-full bg-gray-400"
                    style={{ animationDelay: "0.2s" }}
                  ></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Quick Actions and Input */}
      <div className="space-y-4 border-t border-gray-200 p-4">
        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2">
          {quickActions.map((action) => (
            <Button
              key={action}
              variant="outline"
              size="sm"
              onClick={() => handleQuickAction(action)}
              className="rounded-full border-blue-200 text-blue-600 hover:bg-blue-50"
            >
              {action}
            </Button>
          ))}
        </div>

        {/* Message Input */}
        <div className="flex gap-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Type a message..."
            onKeyPress={(e) => e.key === "Enter" && handleSend()}
            className="flex-1 rounded-full"
          />
          <Button
            onClick={handleSend}
            size="sm"
            className="rounded-full bg-gray-900 px-6 hover:bg-gray-800"
          >
            Send
          </Button>
        </div>
      </div>

      {/* Floating Action Button */}
      <div className="absolute bottom-20 right-4">
        <Button
          size="sm"
          className="size-12 rounded-full bg-orange-500 p-0 shadow-lg hover:bg-orange-600"
        >
          <Bot className="size-6 text-white" />
        </Button>
      </div>
    </div>
  )
}
