import type { Application } from "@/types/application"

import { Card } from "@/components/ui/card"

import { TailorHistoryCard } from "./TailorHistoryCard"

interface TailorHistoryTableProps {
  tailoringHistory: Application[]
  onRefresh: () => Promise<void>
}

export function TailorHistoryTable({
  tailoringHistory,
  onRefresh,
}: TailorHistoryTableProps) {
  return (
    <div>
      {tailoringHistory.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
          {tailoringHistory.map((record) => (
            <TailorHistoryCard key={record.id} record={record} onRefresh={onRefresh} />
          ))}
        </div>
      ) : (
        <Card className="flex justify-center items-center py-10">
          <p className="text-gray-500">No tailoring history found</p>
        </Card>
      )}
    </div>
  )
}
