# OAuth Authentication Troubleshooting Guide

## Problem
OAuth authentication works locally but fails in the deployed version.

## Root Causes Identified

### 1. Environment Variable Issues
- **Local**: `NODE_ENV="development"`, `NEXT_PUBLIC_APP_URL="http://localhost:3000"`
- **Production**: Was set to `NODE_ENV="development"` (incorrect), `NEXT_PUBLIC_APP_URL="https://www.sophie-ai.com"`

### 2. Inconsistent OAuth Redirect URLs
- Multiple OAuth button components with different redirect URL strategies
- Some using `window.location.origin`, others using `process.env.NEXT_PUBLIC_APP_URL`

### 3. Supabase Configuration
- Site URL and redirect URLs need to be configured for both local and production environments

## Fixes Applied

### ✅ 1. Fixed Environment Variables
Updated `frontend/app.yaml`:
```yaml
env_variables:
  NODE_ENV: "production"  # Changed from "development"
  NEXT_PUBLIC_APP_URL: "https://www.sophie-ai.com"
  NEXT_PUBLIC_SUPABASE_URL: "https://nmyamvvgarafizpiobxb.supabase.co"
  NEXT_PUBLIC_SUPABASE_ANON_KEY: "your-anon-key"
```

Updated `docker-compose.yaml` to include Supabase environment variables.

### ✅ 2. Standardized OAuth Components
- Removed duplicate OAuth button component (`frontend/src/components/auth/oauth-buttons.tsx`)
- Updated main component to use robust redirect URL detection
- Updated server action to use consistent redirect URL handling

### ⏳ 3. Supabase Configuration (Manual Steps Required)

## Manual Configuration Steps

### Supabase Dashboard Settings

1. **Navigate to Authentication → Settings → General**
   - Set **Site URL** to: `https://www.sophie-ai.com`

2. **Add Redirect URLs**:
   ```
   http://localhost:3000/auth/callback
   https://www.sophie-ai.com/auth/callback
   ```

3. **OAuth Provider Configuration**
   - Go to **Authentication → Providers → Google**
   - Ensure Google OAuth is enabled
   - Verify Google OAuth client configuration includes the Supabase callback URL

### Google OAuth Console (if needed)
If you need to update your Google OAuth client:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services → Credentials
3. Edit your OAuth 2.0 Client ID
4. Add authorized redirect URIs:
   ```
   https://nmyamvvgarafizpiobxb.supabase.co/auth/v1/callback
   ```

## Testing

### Run OAuth Configuration Test
```bash
cd frontend
npm run test:oauth
# or
node scripts/test-oauth.js
```

### Local Environment Testing
1. **Start the development server**:
   ```bash
   cd frontend
   npm run dev
   ```

2. **Test OAuth Flow**:
   - Navigate to `http://localhost:3000/signin`
   - Click "Sign in with Google"
   - Verify redirect to Google OAuth
   - Complete Google authentication
   - Verify redirect back to `http://localhost:3000/auth/callback`
   - Check if user is properly authenticated and redirected to dashboard

3. **Check for Errors**:
   - Open browser developer tools (F12)
   - Check Console tab for any JavaScript errors
   - Check Network tab for failed requests
   - Verify cookies are being set correctly

### Production Environment Testing
1. **Deploy the updated configuration**:
   ```bash
   # Deploy to your production environment
   # This depends on your deployment method (Amplify, Vercel, etc.)
   ```

2. **Test OAuth Flow**:
   - Navigate to `https://www.sophie-ai.com/signin`
   - Click "Sign in with Google"
   - Verify redirect to Google OAuth
   - Complete Google authentication
   - Verify redirect back to `https://www.sophie-ai.com/auth/callback`
   - Check if user is properly authenticated and redirected to dashboard

3. **Debug Production Issues**:
   - Check browser developer tools for errors
   - Verify environment variables are correctly set in production
   - Check deployment logs for any server-side errors

### Manual Testing Steps
1. **Local Environment**:
   - Start development server: `npm run dev`
   - Navigate to sign-in page
   - Click "Sign in with Google"
   - Verify redirect to Google and back to callback

2. **Production Environment**:
   - Deploy the updated configuration
   - Test OAuth flow on production domain
   - Check browser developer tools for any errors

## Common Issues & Solutions

### Issue: "Invalid redirect URL"
**Solution**: Ensure the redirect URL in Supabase matches exactly what your app is sending.

### Issue: OAuth works locally but not in production
**Solution**: 
- Verify `NEXT_PUBLIC_APP_URL` is set correctly in production
- Check Supabase Site URL configuration
- Ensure SSL certificate is valid on production domain

### Issue: "Access blocked" from Google
**Solution**: 
- Verify your Google OAuth client is configured for production use
- Check if domain verification is required
- Ensure redirect URIs are correctly configured in Google Console

### Issue: User gets redirected but session is not created
**Solution**:
- Check if middleware is interfering with `/auth/callback`
- Verify Supabase anon key is correct
- Check browser cookies are being set correctly

## Verification Checklist

- [ ] Environment variables are correctly set in production
- [ ] Supabase Site URL matches production domain
- [ ] Redirect URLs are configured in Supabase
- [ ] Google OAuth provider is enabled
- [ ] OAuth flow works in local environment
- [ ] OAuth flow works in production environment
- [ ] User session is properly created after OAuth
- [ ] User is redirected to correct page after authentication

## Next Steps

1. Apply the Supabase configuration changes manually
2. Deploy the code changes
3. Test OAuth flow in both environments
4. Monitor for any remaining issues

## Files Modified

- `frontend/app.yaml` - Fixed production environment variables
- `docker-compose.yaml` - Added missing Supabase environment variables
- `frontend/src/components/oauth-buttons.tsx` - Improved redirect URL handling
- `frontend/src/actions/auth/sign-in.ts` - Standardized redirect URL logic
- Removed: `frontend/src/components/auth/oauth-buttons.tsx` - Duplicate component
