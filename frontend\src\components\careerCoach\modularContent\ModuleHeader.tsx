// components/modules/ModuleHeader.tsx
import Image from "next/image"

interface ModuleHeaderProps {
  title: string
  description: string
  imageSrc: string
}

export function ModuleHeader({
  title,
  description,
  imageSrc,
}: ModuleHeaderProps) {
  return (
    <div className="w-full overflow-hidden rounded-xl bg-white">
      <div className="relative h-56 w-full">
        <Image src={imageSrc} alt={title} fill className="object-cover" />
      </div>
      <div className="p-6">
        <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
        <p className="mt-2 text-gray-700">{description}</p>
      </div>
    </div>
  )
}
