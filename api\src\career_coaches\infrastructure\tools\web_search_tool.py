"""Web search tool for retrieving general information."""

import asyncio
import aiohttp
import json
from typing import List, Dict, Any, Optional

from .base_tool import BaseTool, ToolResult
from ...config import settings


class WebSearchTool(BaseTool):
    """Tool for performing web searches using various search APIs."""
    
    def __init__(self, api_keys: Optional[Dict[str, str]] = None):
        super().__init__(
            name="web_search",
            description="Search the web for current information and answers"
        )
        self.api_keys = api_keys or {}
        
        # Search engine endpoints
        self.search_engines = {
            "tavily": self._search_tavily,
            "serper": self._search_serper,
            "ddg": self._search_duckduckgo,
        }
    
    async def execute(self, query: str, **kwargs) -> ToolResult:
        """Execute web search with the given query.
        
        Args:
            query: Search query
            engine: Search engine to use ('tavily', 'serper', 'ddg', 'all')
            max_results: Maximum number of results to return
            
        Returns:
            ToolResult: Web search results
        """
        try:
            engine = kwargs.get('engine', settings.DEFAULT_SEARCH_TOOL)
            max_results = kwargs.get('max_results', 5)
            
            if engine == "all":
                # Search using all available engines
                results = await self._search_all_engines(query, max_results)
            else:
                # Search using specific engine
                results = await self._search_single_engine(query, engine, max_results)
            
            if not results:
                return ToolResult(
                    success=True,
                    data={"results": [], "summary": "No relevant results found."},
                    metadata={"query": query, "engine": engine}
                )
            
            # Generate summary
            summary = self._generate_search_summary(results, query)
            
            return ToolResult(
                success=True,
                data={
                    "results": results,
                    "summary": summary
                },
                metadata={
                    "query": query,
                    "engine": engine,
                    "total_results": len(results)
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Web search failed: {str(e)}",
                metadata={"query": query}
            )
    
    async def _search_all_engines(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """Search using all available engines and combine results."""
        all_results = []
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=settings.SEARCH_TIMEOUT_SECONDS)) as session:
            tasks = []
            for engine_name, engine_func in self.search_engines.items():
                tasks.append(engine_func(session, query, max_results // len(self.search_engines) + 1))
            
            engine_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for results in engine_results:
                if isinstance(results, list):
                    all_results.extend(results)
        
        # Deduplicate and limit results
        return self._deduplicate_results(all_results)[:max_results]
    
    async def _search_single_engine(self, query: str, engine: str, max_results: int) -> List[Dict[str, Any]]:
        """Search using a single engine."""
        if engine not in self.search_engines:
            raise ValueError(f"Unknown search engine: {engine}")
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=settings.SEARCH_TIMEOUT_SECONDS)) as session:
            return await self.search_engines[engine](session, query, max_results)
    
    async def _search_tavily(self, session: aiohttp.ClientSession, query: str, max_results: int) -> List[Dict[str, Any]]:
        """Search using Tavily API."""
        try:
            if "tavily" not in self.api_keys:
                return []
            
            url = "https://api.tavily.com/search"
            payload = {
                "api_key": self.api_keys["tavily"],
                "query": query,
                "search_depth": "basic",
                "include_answer": True,
                "include_raw_content": False,
                "max_results": max_results
            }
            
            async with session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []
                    
                    for result in data.get("results", []):
                        results.append({
                            "title": result.get("title", ""),
                            "snippet": result.get("content", ""),
                            "url": result.get("url", ""),
                            "source": "tavily"
                        })
                    
                    return results
                    
        except Exception as e:
            print(f"Tavily search error: {e}")
        
        return []
    
    async def _search_serper(self, session: aiohttp.ClientSession, query: str, max_results: int) -> List[Dict[str, Any]]:
        """Search using Serper API."""
        try:
            if "serper" not in self.api_keys:
                return []
            
            url = "https://google.serper.dev/search"
            headers = {
                "X-API-KEY": self.api_keys["serper"],
                "Content-Type": "application/json"
            }
            payload = {
                "q": query,
                "num": max_results
            }
            
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []
                    
                    for result in data.get("organic", []):
                        results.append({
                            "title": result.get("title", ""),
                            "snippet": result.get("snippet", ""),
                            "url": result.get("link", ""),
                            "source": "serper"
                        })
                    
                    return results
                    
        except Exception as e:
            print(f"Serper search error: {e}")
        
        return []
    
    async def _search_duckduckgo(self, session: aiohttp.ClientSession, query: str, max_results: int) -> List[Dict[str, Any]]:
        """Search using DuckDuckGo (simplified implementation)."""
        try:
            # This is a simplified implementation
            # In practice, you might want to use a proper DuckDuckGo API client
            url = f"https://api.duckduckgo.com/?q={query}&format=json&no_html=1"
            
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []
                    
                    # DuckDuckGo instant answer
                    if data.get("Abstract"):
                        results.append({
                            "title": data.get("Heading", query),
                            "snippet": data.get("Abstract", ""),
                            "url": data.get("AbstractURL", ""),
                            "source": "duckduckgo"
                        })
                    
                    # Related topics
                    for topic in data.get("RelatedTopics", [])[:max_results-1]:
                        if isinstance(topic, dict) and "Text" in topic:
                            results.append({
                                "title": topic.get("FirstURL", {}).get("Text", ""),
                                "snippet": topic.get("Text", ""),
                                "url": topic.get("FirstURL", {}).get("Result", ""),
                                "source": "duckduckgo"
                            })
                    
                    return results[:max_results]
                    
        except Exception as e:
            print(f"DuckDuckGo search error: {e}")
        
        return []
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate results based on URL and title similarity."""
        seen_urls = set()
        seen_titles = set()
        unique_results = []
        
        for result in results:
            url = result.get("url", "").lower()
            title = result.get("title", "").lower().strip()
            
            # Skip if we've seen this URL or very similar title
            if url in seen_urls or title in seen_titles:
                continue
            
            if url:
                seen_urls.add(url)
            if title:
                seen_titles.add(title)
            
            unique_results.append(result)
        
        return unique_results
    
    def _generate_search_summary(self, results: List[Dict[str, Any]], query: str) -> str:
        """Generate a summary of search results."""
        if not results:
            return "No relevant search results found."
        
        summary = f"Found {len(results)} relevant results for '{query}':\n\n"
        
        for i, result in enumerate(results[:3], 1):  # Summarize top 3
            summary += f"{i}. **{result['title']}**\n"
            if result['snippet']:
                snippet = result['snippet'][:200]
                if len(result['snippet']) > 200:
                    snippet += "..."
                summary += f"   {snippet}\n"
            summary += f"   Source: {result['source']}\n\n"
        
        if len(results) > 3:
            summary += f"... and {len(results) - 3} more results.\n"
        
        return summary
