import { createClient } from "@/lib/supabase/server"
import { NextResponse } from "next/server"
import { type NextRequest } from "next/server"

export async function GET(request: NextRequest) {
  try {
    const requestUrl = new URL(request.url)
    const code = requestUrl.searchParams.get("code")
    
    if (!code) {
      throw new Error("No code provided")
    }

    const supabase = await createClient()
    const { data: { user }, error } = await supabase.auth.exchangeCodeForSession(code)
    
    if (error) {
      console.error("Auth exchange error:", error)
      throw error
    }
    
    if (!user) {
      throw new Error("No user returned from auth exchange")
    }

    // Check if profile exists
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError && profileError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error fetching profile:', profileError)
      throw profileError
    }

    // If no profile exists, create one with basic info
    if (!profile && user) {
      // Create a minimal profile - the email is already associated with the auth.users table
      const { error: createError } = await supabase
        .from('profiles')
        .insert([
          {
            id: user.id,
            first_name: "", // We'll collect this in onboarding
            last_name: "",  // We'll collect this in onboarding
            created_at: new Date().toISOString(),
          }
        ])
      
      if (createError) {
        console.error('Error creating profile:', createError)
        throw createError
      }
      
      // Redirect to onboarding to complete profile
      return NextResponse.redirect(new URL("/onboarding", process.env.NEXT_PUBLIC_APP_URL))
    }

    // If profile exists but incomplete (missing required fields)
    if (profile && (!profile.first_name || !profile.last_name)) {
      return NextResponse.redirect(new URL("/onboarding", process.env.NEXT_PUBLIC_APP_URL))
    }

    // Redirect to dashboard/app for complete profiles
    return NextResponse.redirect(new URL("/app", process.env.NEXT_PUBLIC_APP_URL))
  } catch (error) {
    console.error('Auth callback error:', error)
    return NextResponse.redirect(
      new URL("/signin?error=" + encodeURIComponent("Authentication failed"), 
      process.env.NEXT_PUBLIC_APP_URL)
    )
  }
}