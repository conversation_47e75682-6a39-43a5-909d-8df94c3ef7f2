import { Spark<PERSON> } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface TailorNotificationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function TailorNotificationDialog({
  open,
  onOpenChange,
}: TailorNotificationDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Request Submitted Successfully!</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <div className="mb-4 flex items-center gap-3">
            <div className="flex size-12 items-center justify-center rounded-full bg-green-100">
              <Sparkles className="size-6 text-green-600" />
            </div>
            <div>
              <p className="font-medium text-gray-900">
                Your tailoring request has been submitted!
              </p>
              <p className="mt-1 text-sm text-gray-700">
                Your documents are being generated in the background. This process may take
                up to 1-2 minutes to complete.
              </p>
            </div>
          </div>

          <div className="mb-4 rounded-lg bg-blue-50 p-4">
            <p className="mb-1 text-sm font-medium text-blue-800">
              What happens next?
            </p>
            <ul className="space-y-1 text-sm text-blue-700">
              <li>• We&apos;ll analyze the job description and requirements</li>
              <li>• Customize your resume content to highlight relevant skills</li>
              <li>• Generate a tailored cover letter that matches the position</li>
              <li>• The progress will be shown in the history table</li>
            </ul>
          </div>

          <div className="space-y-2 text-center">
            <p className="text-sm text-gray-600">
              You can continue using other parts of the website while we work on
              your documents.
            </p>
            <p className="text-sm font-medium text-gray-800">
              Check the history table for live status updates and to download your
              completed documents when they&apos;re ready.
            </p>
          </div>
        </div>

        <div className="flex justify-end">
          <Button onClick={() => onOpenChange(false)}>Got it!</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
