export type ApplicationStatus = "Pending" | "In Progress" | "Partially Completed" | "Completed" | "Failed"

export type DocumentStatus = "pending" | "processing" | "completed" | "failed"

export interface Job {
  id: string
  company_id: string
  job_title: string
  job_url?: string
  job_description: string
  created_at: string
  updated_at: string
}

export interface Company {
  id: string
  name: string
  created_at: string
  updated_at: string
}

export interface Document {
  id: string
  application_id: string
  document_type: 'resume' | 'cover_letter'
  status: DocumentStatus
  file_url?: string
  content?: string
  created_at: string
  updated_at: string
  completed_at?: string
  error_message?: string
  request_id: string
}

export interface DocumentVersion {
  id: string
  document_id: string
  version: number
  file_url?: string
  content?: string
  created_at: string
  status: DocumentStatus
  error_message?: string
}

export interface Application {
  id: string
  user_id: string
  resume_id: string
  job_id: string
  company_id: string
  applied_date: string
  status: ApplicationStatus
  notes?: string
  created_at: string
  updated_at: string
  job: Job
  company: Company
  documents?: Document[]
}
