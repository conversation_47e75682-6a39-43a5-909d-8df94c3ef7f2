"use client"

import Image from "next/image"

const userPaths = [
  {
    title: "Not Sure What Jobs Fit You?",
    description:
      "Use our AI Career Coach to explore career paths that match your personality, skills, and interests.",
    image: "/images/features/sh1.png",
    icon: "🤖",
  },
  {
    title: "Know What You Want, But No Resume?",
    description:
      "Build a polished, tailored resume from scratch with SophieAI’s intelligent resume builder.",
    image: "/images/features/sh1.png",
    icon: "📄",
  },
  {
    title: "Applying to Dream Jobs?",
    description:
      "Upload your resume and target job post—we’ll tailor it for maximum impact with Resume Refiner.",
    image: "/images/features/sh1.png",
    icon: "🎯",
  },
  {
    title: "Getting Ghosted After Applying?",
    description:
      "Use Interview Rate Booster to optimize your resume for ATS and recruiter appeal.",
    image: "/images/features/sh1.png",
    icon: "📈",
  },
  {
    title: "Mass Applying Every Day?",
    description:
      "Autofill job forms in seconds with our browser extension—built for speed and scale.",
    image: "/images/features/sh1.png",
    icon: "⚡",
  },
]

export function UserPathsSection() {
  return (
    <section id="user-paths" className="w-full bg-white py-24">
      <div className="container space-y-12">
        <div className="text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            Tailored Tools for Every Job Seeker
          </h2>
          <p className="mx-auto mt-2 max-w-2xl text-muted-foreground">
            Whether you’re just getting started or already deep in the job hunt,
            Sophie adapts to where you are.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {userPaths.map((item, idx) => (
            <div
              key={idx}
              className="flex flex-col gap-4 rounded-2xl border bg-muted/10 p-6 shadow-sm"
            >
              <div className="text-3xl">{item.icon}</div>
              <h3 className="text-lg font-semibold">{item.title}</h3>
              <p className="text-sm text-muted-foreground">
                {item.description}
              </p>
              <div className="relative mt-auto h-48 w-full overflow-hidden rounded-lg border">
                <Image
                  src={item.image}
                  alt={item.title}
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
