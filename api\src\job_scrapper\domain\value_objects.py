from dataclasses import dataclass
from urllib.parse import urlparse
from typing import Optional

@dataclass(frozen=True)
class URL:
    value: str

    def __post_init__(self):
        if not self._is_valid_url():
            raise ValueError(f"Invalid URL: {self.value}")

    def _is_valid_url(self) -> bool:
        try:
            result = urlparse(self.value)
            return all([result.scheme in ["http", "https"], result.netloc])
        except ValueError:
            return False