import Link from "next/link"
import { Suspense } from "react"

import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { SignUpWithPasswordForm } from "@/components/forms/signup-with-password-form"
import { Icons } from "@/components/icons"
import { OAuthButtons } from "@/components/oauth-buttons"

export default function SignUpPage() {
  return (
    <div className="flex h-auto min-h-screen w-full items-center justify-center bg-secondary/20">
      <Card className="w-full max-w-[420px] sm:rounded-lg sm:border sm:shadow-lg">
        <CardHeader className="space-y-1 pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold text-primary">Sign up</CardTitle>
            <Link href="/" className="rounded-full p-1 hover:bg-secondary transition-colors">
              <Icons.close className="size-4" />
            </Link>
          </div>
          <CardDescription className="text-sm">
            Create your InternUp account
          </CardDescription>
        </CardHeader>
        <CardContent className="w-full px-6 sm:px-8">
          <OAuthButtons />
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative mb-3 mt-6 flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or continue with password
              </span>
            </div>
          </div>
          <Suspense fallback={<div className="w-full py-6 text-center">Loading sign up form...</div>}>
            <SignUpWithPasswordForm />
          </Suspense>
        </CardContent>
        <CardFooter className="flex flex-col w-full gap-3 px-6 text-sm text-muted-foreground sm:px-8">
          <div className="w-full text-center">
            <span> Already have an account? </span>
            <Link
              aria-label="Sign in"
              href="/signin"
              className="font-semibold text-primary underline-offset-4 transition-all hover:underline"
            >
              Sign in
              <span className="sr-only">Sign in</span>
            </Link>
          </div>
          <div className="w-full text-center text-xs">
            <span>Lost verification email? </span>
            <Link
              aria-label="Resend email verification link"
              href="/signup/reverify-email"
              className="text-xs font-semibold text-primary underline-offset-4 transition-colors hover:underline"
            >
              Resend
              <span className="sr-only">Resend email verification link</span>
            </Link>
          </div>

          <div className="text-xs text-center text-muted-foreground mt-2">
            By continuing, you agree to our{" "}
            <Link
              aria-label="Terms of Service"
              href="/tos"
              className="font-semibold underline-offset-4 transition-all hover:underline"
            >
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link
              aria-label="Privacy Policy"
              href="/privacy"
              className="font-semibold underline-offset-4 transition-all hover:underline"
            >
              Privacy Policy
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
