Arguments: 
  /Users/<USER>/.nvm/versions/node/v20.13.1/bin/node /Users/<USER>/.cache/node/corepack/yarn/1.22.19/bin/yarn.js install

PATH: 
  /Users/<USER>/google-cloud-sdk/bin:/Users/<USER>/.bun/bin:/Users/<USER>/.nvm/versions/node/v20.13.1/bin:/opt/anaconda3/bin:/opt/anaconda3/condabin:/Library/Frameworks/Python.framework/Versions/3.11/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/usr/local/go/bin:/Applications/Postgres.app/Contents/Versions/latest/bin:/opt/homebrew/bin:/Users/<USER>/Development/tools/apache-maven-3.9.6/bin:/Applications/Postgres.app/Contents/Versions/16/bin

Yarn version: 
  1.22.19

Node version: 
  20.13.1

Platform: 
  darwin arm64

Trace: 
  Error: ENOENT: no such file or directory, scandir '/Users/<USER>/Documents/InternUp/SophieAI/node_modules/@shadcn/ui/node_modules/ora/node_modules'

npm manifest: 
  {
    "private": true,
    "name": "sophie-ai",
    "description": "Resume AI",
    "version": "0.2.0",
    "license": "MIT",
    "author": {
      "name": "Harsimran Singh",
      "url": "https://github.com/Harsimran-19"
    },
    "repository": {
      "type": "git",
      "url": "https://github.com/Harsimran-19/SophieAI"
    },
    "bugs": {
      "url": "https://github.com/Harsimran-19/SophieAI/issues"
    },
    "type": "module",
    "scripts": {
      "dev": "next dev",
      "build": "next build",
      "start": "next start",
      "shadcn:add": "pnpm dlx shadcn-ui@latest add",
      "packages:update-all": "pnpm update --latest",
      "prettier:format": "prettier --write \"**/*.{ts,tsx,mdx}\" --cache",
      "prettier:check": "prettier --check \"**/*.{ts,tsx,mdx}\" --cache",
      "typecheck": "tsc --noEmit",
      "lint": "next lint",
      "lint:fix": "next lint --fix"
    },
    "dependencies": {
      "@ai-sdk/google-vertex": "^2.1.4",
      "@auth/core": "^0.30.0",
      "@dnd-kit/core": "^6.3.1",
      "@dnd-kit/sortable": "^10.0.0",
      "@dnd-kit/utilities": "^3.2.2",
      "@hookform/resolvers": "^3.3.4",
      "@hpcc-js/wasm": "^2.22.4",
      "@radix-ui/react-accordion": "^1.1.2",
      "@radix-ui/react-alert-dialog": "^1.0.5",
      "@radix-ui/react-aspect-ratio": "^1.0.3",
      "@radix-ui/react-avatar": "^1.0.4",
      "@radix-ui/react-checkbox": "^1.0.4",
      "@radix-ui/react-collapsible": "^1.0.3",
      "@radix-ui/react-context-menu": "^2.1.5",
      "@radix-ui/react-dialog": "^1.0.5",
      "@radix-ui/react-dropdown-menu": "^2.0.6",
      "@radix-ui/react-hover-card": "^1.0.7",
      "@radix-ui/react-icons": "^1.3.0",
      "@radix-ui/react-label": "^2.0.2",
      "@radix-ui/react-menubar": "^1.0.4",
      "@radix-ui/react-navigation-menu": "^1.1.4",
      "@radix-ui/react-popover": "^1.0.7",
      "@radix-ui/react-portal": "^1.1.4",
      "@radix-ui/react-progress": "^1.0.3",
      "@radix-ui/react-radio-group": "^1.1.3",
      "@radix-ui/react-scroll-area": "^1.0.5",
      "@radix-ui/react-select": "^2.0.0",
      "@radix-ui/react-separator": "^1.0.3",
      "@radix-ui/react-slider": "^1.1.2",
      "@radix-ui/react-slot": "^1.0.2",
      "@radix-ui/react-switch": "^1.0.3",
      "@radix-ui/react-tabs": "^1.0.4",
      "@radix-ui/react-toast": "^1.1.5",
      "@radix-ui/react-toggle": "^1.0.3",
      "@radix-ui/react-tooltip": "^1.0.7",
      "@react-email/components": "^0.0.17",
      "@react-pdf/renderer": "^4.2.2",
      "@studio-freight/react-lenis": "^0.0.47",
      "@supabase/ssr": "^0.5.2",
      "@supabase/supabase-js": "^2.47.10",
      "@t3-oss/env-nextjs": "^0.7.1",
      "@vercel/analytics": "^1.2.2",
      "ai": "^4.1.7",
      "axios": "^1.7.9",
      "bcryptjs": "^2.4.3",
      "bufferutil": "^4.0.8",
      "class-variance-authority": "^0.7.0",
      "clsx": "^2.1.1",
      "cmdk": "^1.0.0",
      "date-fns": "^3.6.0",
      "dotenv": "^16.4.5",
      "eslint": "^8.57.1",
      "framer-motion": "^11.1.9",
      "html-pdf-node": "^1.0.8",
      "http": "0.0.1-security",
      "jsonwebtoken": "^9.0.2",
      "lucide-react": "^0.469.0",
      "mathjax-full": "^3.2.2",
      "next": "14.2.3",
      "next-auth": "5.0.0-beta.11",
      "next-themes": "^0.3.0",
      "nodemailer": "^6.9.13",
      "pdfjs-dist": "^4.10.38",
      "react": "^18.3.1",
      "react-day-picker": "^8.10.1",
      "react-dom": "^18.3.1",
      "react-email": "^2.1.2",
      "react-hook-form": "^7.51.4",
      "react-markdown": "^9.0.3",
      "react-pdf": "^9.2.1",
      "react-resizable-panels": "^2.1.7",
      "react-wrap-balancer": "^1.1.0",
      "recharts": "^2.15.0",
      "resend": "^3.2.0",
      "sharp": "^0.33.5",
      "tailwind-merge": "^2.3.0",
      "tailwindcss-animate": "^1.0.7",
      "uuid": "^11.0.5",
      "ws": "^8.17.0",
      "zod": "^3.23.8",
      "zustand": "^5.0.3"
    },
    "devDependencies": {
      "@ianvs/prettier-plugin-sort-imports": "^4.2.1",
      "@shadcn/ui": "^0.0.4",
      "@tailwindcss/line-clamp": "^0.4.4",
      "@tailwindcss/typography": "^0.5.13",
      "@types/bcryptjs": "^2.4.6",
      "@types/eslint": "^8.56.10",
      "@types/html-pdf-node": "^1.0.2",
      "@types/jsonwebtoken": "^9.0.6",
      "@types/node": "^20.12.11",
      "@types/react": "^18.3.1",
      "@types/react-dom": "^18.3.0",
      "@types/ws": "^8.5.10",
      "@typescript-eslint/eslint-plugin": "^7.8.0",
      "@typescript-eslint/parser": "^7.8.0",
      "autoprefixer": "^10.4.19",
      "eslint-config-next": "14.2.3",
      "eslint-config-prettier": "^9.1.0",
      "eslint-plugin-tailwindcss": "^3.15.1",
      "postcss": "^8.4.38",
      "prettier": "^3.2.5",
      "prettier-plugin-tailwindcss": "^0.5.14",
      "rehype": "^13.0.1",
      "rehype-autolink-headings": "^7.1.0",
      "rehype-pretty-code": "^0.13.1",
      "rehype-slug": "^6.0.0",
      "remark": "^15.0.1",
      "remark-gfm": "^4.0.0",
      "tailwindcss": "^3.4.3",
      "typescript": "^5.4.5"
    },
    "engine": {
      "node": ">=20.10.0"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  No lockfile
