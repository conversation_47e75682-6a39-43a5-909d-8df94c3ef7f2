// app/api/copilotkit/route.ts

import { NextRequest } from "next/server"

import { createClient } from "@/lib/supabase/server"
import { BASE_API_URL } from "@/config/apiEndpoints"

// Default coach ID (used as fallback)
const API_URL =
`${BASE_API_URL}/career-coach/chat`

export async function POST(req: NextRequest) {
  try {
    const supabase = createClient()
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser()

    if (userError || !user) {
      console.error("❌ Unauthorized:", userError)
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      })
    }

    const userId = user.id
    const requestBody = await req.json()

    if (requestBody.operationName !== "generateCopilotResponse") {
      return new Response(JSON.stringify({ message: "Not a chat request" }), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      })
    }

    const messages = requestBody?.variables?.data?.messages
    const userContext =
      requestBody?.variables?.data?.metadata?.userContext || ""

    if (!Array.isArray(messages)) {
      console.error("❌ Invalid messages:", messages)
      return new Response(JSON.stringify({ error: "Invalid messages" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      })
    }

    const userMessage = messages
      .slice()
      .reverse()
      .find((m) => m.textMessage?.role === "user")?.textMessage?.content

    if (!userMessage) {
      console.error(
        "❌ No user message found. Sample structure:",
        JSON.stringify(messages, null, 2)
      )

      return new Response(JSON.stringify({ error: "Missing user message" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      })
    }

    const coachId = requestBody?.variables?.data?.metadata?.coachId || "career_assessment"

    const payload = {
      message: userMessage,
      coach_id: coachId,
      user_id: userId,
      user_context: userContext,
      session_goals: [],
    }

    const apiRes = await fetch(API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    })

    if (!apiRes.ok) {
      const errorText = await apiRes.text()
      console.error("❌ Coach API Error:", errorText)
      return new Response(JSON.stringify({ error: "Coach API failed" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      })
    }

    const result = await apiRes.json()
    const assistantMessage =
      result?.response ?? result?.message ?? "No response."

    return new Response(
      JSON.stringify({
        data: {
          generateCopilotResponse: {
            messages: [
              {
                __typename: "TextMessageOutput",
                role: "assistant",
                content: [assistantMessage],
                parentMessageId: null,
              },
            ],
            status: {
              __typename: "BaseResponseStatus",
              code: 200,
            },
            runId: "placeholder-run",
            threadId: "placeholder-thread",
            extensions: {},
          },
        },
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" },
      }
    )
  } catch (err) {
    console.error("❌ Unexpected error in /api/copilotkit:", err)
    return new Response(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    })
  }
}
