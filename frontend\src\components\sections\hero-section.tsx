"use client"

import Link from "next/link"

import { useUser } from "@/hooks/use-user"
import { cn } from "@/lib/utils"

import { buttonVariants } from "@/components/ui/button"

export function HeroSection() {
  const { user } = useUser()

  return (
    <section id="hero-section" className="mt-24 w-full md:mt-32">
      <div className="container flex flex-col items-center gap-5 text-center">
        <h1 className="font-urbanist text-4xl font-extrabold tracking-tight sm:text-5xl md:text-6xl">
          Your AI Career Coach. <br className="hidden sm:block" />
          <span className="relative inline-block">
            Build Your Dream Career with{" "}
            <span className="underline decoration-blue-400 decoration-[0.3em] underline-offset-[0.3em]">
              AI-Power
            </span>
          </span>
        </h1>

        <Link
          href={user ? "/app" : "/signup"}
          className={cn(
            buttonVariants({ size: "lg" }),
            "mt-4 px-8 py-4 text-base font-medium"
          )}
        >
          {user ? "Go to Dashboard" : "Sign Up — It’s Free!"}
        </Link>

        <p className="mt-2 text-sm text-muted-foreground">
          ⭐ Join 1,000+ job seekers using SophieAI
        </p>
      </div>
    </section>
  )
}
