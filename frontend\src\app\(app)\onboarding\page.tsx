import { redirect } from "next/navigation"

import { createClient } from "@/lib/supabase/server"

import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default async function OnboardingPage() {
  const supabase = await createClient()
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser()

  if (!user || userError) {
    redirect("/signin")
  }

  const { data: profile, error: profileError } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", user.id)
    .single()

  if (profileError && profileError.code !== "PGRST116") {
    console.error("Profile fetch error:", profileError)
    redirect("/signin?error=profile_error")
  }

  if (profile?.first_name && profile?.last_name) {
    redirect("/app")
  }

  return (
    <main className="container flex min-h-screen w-full flex-col items-center justify-center">
      <Card className="w-full max-w-md p-6">
        <div className="mb-6 text-center">
          <h1 className="text-2xl font-semibold">Complete Your Profile</h1>
          <p className="text-sm text-muted-foreground">
            We just need your name to finish setting up.
          </p>
        </div>
        <form action="/onboarding/submit" method="POST" className="space-y-4">
          <div>
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              name="firstName"
              defaultValue={profile?.first_name || ""}
              required
            />
          </div>
          <div>
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              name="lastName"
              defaultValue={profile?.last_name || ""}
              required
            />
          </div>
          <Button type="submit" className="w-full">
            Continue
          </Button>
        </form>
      </Card>
    </main>
  )
}
