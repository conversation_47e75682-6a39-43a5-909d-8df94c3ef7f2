version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://api:8000
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
      - NEXT_PUBLIC_SUPABASE_URL=https://nmyamvvgarafizpiobxb.supabase.co
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5teWFtdnZnYXJhZml6cGlvYnhiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUxOTg3NzksImV4cCI6MjA1MDc3NDc3OX0.zgUxyA2s9wtgxx_7yoRACFM5S0vofRmdejEFVGI2vqE
    depends_on:
      - api
    # Development volumes removed to use the built app
    # For development, uncomment these volume mounts
    # volumes:
    #  - ./frontend:/app
    #  - /app/node_modules
    #  - /app/.next
    networks:
      - sophie-network
    restart: unless-stopped

  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app:/app/src
    volumes:
      - ./api:/app
      - /app/__pycache__
    networks:
      - sophie-network
    restart: unless-stopped

networks:
  sophie-network:
    driver: bridge