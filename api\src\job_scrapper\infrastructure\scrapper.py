from tavily import <PERSON>ly<PERSON><PERSON>
from langchain_groq import <PERSON>tGroq
from ..domain.services import ScraperPort
from ..domain.entities import JobPosting, JobDescription, CompanyDescription
from ..domain.value_objects import URL
from src.common.config.job_scrapper_settings import job_scrapper_settings
from typing import Optional, Dict, Any
import logging
import json

logger = logging.getLogger(__name__)

class TavilyJobScraper(ScraperPort):
    def __init__(self):
        self.tavily_client = TavilyClient(api_key=job_scrapper_settings.TAVILY_API_KEY)
        self.llm = ChatGroq(
            model=job_scrapper_settings.LLM_MODEL,
            temperature=job_scrapper_settings.LLM_TEMPERATURE,
            groq_api_key=job_scrapper_settings.GROQ_API_KEY
        )
        self.extraction_prompt = """
        You are an expert in extracting job information from web content.
        The following text is from a job posting web page.
        
        Extract and structure the following information:
        1. Job Description: Provide a comprehensive summary of the job role, responsibilities, and requirements.
        2. Company Description: Extract information about the company, its culture, values, and background.
        3. Company Name: Extract the exact company name.
        
        Format your response as a JSON object with these keys:
        - 'job_description': The extracted job description as a PLAIN TEXT STRING (not a nested object)
        - 'company_description': The extracted company description as a PLAIN TEXT STRING (not a nested object)
        - 'company_name': The name of the company as a simple string
        
        IMPORTANT: All fields MUST be simple strings, NOT objects or nested structures.
        If any section is not available in the content, return null for that field.
        
        Ensure all content is properly formatted text and does not include irrelevant content like navigation menus, footers, or advertisements.
        
        Web content:
        {web_content}
        
        JSON Response:
        """

    async def scrape(self, url: URL) -> Optional[JobPosting]:
        try:
            # Use Tavily's extract method to directly retrieve webpage content
            extract_result = self.tavily_client.extract(
                urls=[url.value],
                extract_depth=job_scrapper_settings.EXTRACT_DEPTH,
                format="text"
            )
            
            # Get the extracted content
            web_content = ""
            if extract_result and extract_result.get("results"):
                web_content = extract_result["results"][0].get("raw_content", "")
                
            if not web_content:
                logger.warning(f"No content extracted for URL: {url.value}")
                return None
            
            # Process the web content with LLM to extract structured job information
            formatted_prompt = self.extraction_prompt.format(web_content=web_content)
            extraction_response = self.llm.invoke(formatted_prompt)
            
            # Parse the JSON response from the LLM
            try:
                # Extract JSON from the response text
                response_text = extraction_response.content
                # Find JSON content if wrapped in markdown code block
                if "```json" in response_text and "```" in response_text.split("```json", 1)[1]:
                    json_content = response_text.split("```json", 1)[1].split("```", 1)[0].strip()
                else:
                    json_content = response_text
                
                logger.info(f"LLM response content for {url.value}: {json_content[:500]}...")
                    
                extracted_data = json.loads(json_content)
                job_desc = extracted_data.get("job_description")
                company_desc = extracted_data.get("company_description")
                company_name = extracted_data.get("company_name", "")
                
                # Log the type and structure of extracted data
                logger.info(f"Job description type: {type(job_desc)}, Company description type: {type(company_desc)}")
                
                # Convert dictionaries to strings if needed
                if isinstance(job_desc, dict):
                    logger.info(f"Converting job description dict to string. Keys: {job_desc.keys()}")
                    job_desc = json.dumps(job_desc)
                
                if isinstance(company_desc, dict):
                    logger.info(f"Converting company description dict to string. Keys: {company_desc.keys()}")
                    company_desc = json.dumps(company_desc)
                
                return JobPosting(
                    job_description=JobDescription(content=job_desc) if job_desc else None,
                    company_description=CompanyDescription(content=company_desc) if company_desc else None,
                    company_name=company_name
                )
            except json.JSONDecodeError as je:
                logger.error(f"Error parsing LLM JSON response for {url.value}: {str(je)}")
                return None
                
        except Exception as e:
            logger.error(f"Tavily scraping error for {url.value}: {str(e)}")
            return None