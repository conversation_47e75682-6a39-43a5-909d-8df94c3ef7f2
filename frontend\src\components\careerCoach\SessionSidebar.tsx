import { useState } from "react"
import { useRouter } from "next/navigation"
import {
  ArrowLeft,
  User,
  MessageSquare,
  Plus,
  MoreVertical,
  Edit3,
  Trash2,
  Clock,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"

interface ChatSession {
  id: string
  chat_session_id: string
  title: string
  created_at: string
  updated_at: string
  message_count?: number
}

interface Coach {
  id: string
  name: string
  specialty: string
}

interface SessionSidebarProps {
  coach: Coach
  sessions: ChatSession[]
  currentSessionId: string
  onCreateSession: (title?: string) => void
  onRenameSession?: (sessionId: string, newTitle: string) => void
  onDeleteSession?: (sessionId: string) => void
  className?: string
}

export default function SessionSidebar({
  coach,
  sessions,
  currentSessionId,
  onCreateSession,
  onRenameSession,
  onDeleteSession,
  className = "",
}: SessionSidebarProps) {
  const router = useRouter()
  const [newSessionTitle, setNewSessionTitle] = useState("")
  const [editingSession, setEditingSession] = useState<string | null>(null)
  const [editTitle, setEditTitle] = useState("")
  const [isCreatingSession, setIsCreatingSession] = useState(false)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return "Yesterday"
    if (diffDays < 7) return `${diffDays} days ago`
    return date.toLocaleDateString()
  }

  const handleCreateSession = async () => {
    setIsCreatingSession(true)
    try {
      await onCreateSession(newSessionTitle || undefined)
      setNewSessionTitle("")
    } finally {
      setIsCreatingSession(false)
    }
  }

  const handleRenameSession = (sessionId: string) => {
    if (onRenameSession && editTitle.trim()) {
      onRenameSession(sessionId, editTitle.trim())
      setEditingSession(null)
      setEditTitle("")
    }
  }

  const startEditing = (session: ChatSession) => {
    setEditingSession(session.id)
    setEditTitle(session.title)
  }

  return (
    <div className={`flex h-screen flex-col bg-white border-r ${className}`}>
      {/* Sidebar Header */}
      <div className="border-b p-4">
        <div className="flex items-center gap-3 mb-3">
          <div className="flex size-10 items-center justify-center rounded-full bg-blue-100">
            <User className="size-5 text-blue-600" />
          </div>
          <div>
            <h2 className="font-semibold text-gray-900">{coach.name}</h2>
            <p className="text-sm text-gray-600">{coach.specialty}</p>
          </div>
        </div>
        
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="w-full">
              <Plus className="mr-2 size-4" />
              New Session
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Start New Session</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Session Title (Optional)
                </label>
                <Input
                  value={newSessionTitle}
                  onChange={(e) => setNewSessionTitle(e.target.value)}
                  placeholder="e.g., Resume Review, Interview Prep..."
                  className="mt-1"
                />
              </div>
              <div className="flex gap-3">
                <Button
                  onClick={handleCreateSession}
                  disabled={isCreatingSession}
                  className="flex-1"
                >
                  {isCreatingSession ? "Creating..." : "Start Session"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Session History */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-700">Recent Sessions</h3>
          <Badge variant="secondary" className="text-xs">
            {sessions.length}
          </Badge>
        </div>
        
        {sessions.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="mx-auto mb-2 size-8 text-gray-400" />
            <p className="text-sm text-gray-500">No sessions yet</p>
            <p className="text-xs text-gray-400">Start your first conversation</p>
          </div>
        ) : (
          <div className="space-y-2">
            {sessions.map((session) => (
              <Card
                key={session.id}
                className={`cursor-pointer transition-all hover:bg-gray-50 ${
                  session.chat_session_id === currentSessionId
                    ? "border-blue-200 bg-blue-50"
                    : "border-gray-200"
                }`}
                onClick={() => {
                  if (session.chat_session_id !== currentSessionId) {
                    router.push(`/app/careerCoach/${coach.id}/${session.chat_session_id}`)
                  }
                }}
              >
                <CardContent className="p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      {editingSession === session.id ? (
                        <div className="space-y-2" onClick={(e) => e.stopPropagation()}>
                          <Input
                            value={editTitle}
                            onChange={(e) => setEditTitle(e.target.value)}
                            className="text-sm"
                            onKeyPress={(e) => {
                              if (e.key === "Enter") {
                                handleRenameSession(session.id)
                              } else if (e.key === "Escape") {
                                setEditingSession(null)
                                setEditTitle("")
                              }
                            }}
                            autoFocus
                          />
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleRenameSession(session.id)}
                              className="h-6 px-2 text-xs"
                            >
                              Save
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                setEditingSession(null)
                                setEditTitle("")
                              }}
                              className="h-6 px-2 text-xs"
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <>
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {session.title}
                          </h4>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex items-center gap-1">
                              <Clock className="size-3 text-gray-400" />
                              <span className="text-xs text-gray-600">
                                {formatDate(session.updated_at)}
                              </span>
                            </div>
                            {session.message_count && (
                              <div className="flex items-center gap-1">
                                <MessageSquare className="size-3 text-gray-400" />
                                <span className="text-xs text-gray-500">
                                  {session.message_count}
                                </span>
                              </div>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                    
                    {editingSession !== session.id && (onRenameSession || onDeleteSession) && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreVertical className="size-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {onRenameSession && (
                            <DropdownMenuItem onClick={() => startEditing(session)}>
                              <Edit3 className="mr-2 size-3" />
                              Rename
                            </DropdownMenuItem>
                          )}
                          {onDeleteSession && (
                            <DropdownMenuItem
                              onClick={() => onDeleteSession(session.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 size-3" />
                              Delete
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Sidebar Footer */}
      <div className="border-t p-4">
        <Button
          onClick={() => router.push("/app/careerCoach")}
          variant="ghost"
          size="sm"
          className="w-full justify-start"
        >
          <ArrowLeft className="mr-2 size-4" />
          Back to Coaches
        </Button>
      </div>
    </div>
  )
}
