"use client"

import * as React from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { signUpWithPassword } from "@/actions/auth/sign-up"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"

import {
  signUpWithPasswordSchema,
  type SignUpWithPasswordFormInput,
} from "@/validations/auth"

import { useToast } from "@/hooks/use-toast"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Icons } from "@/components/icons"
import { PasswordInput } from "@/components/password-input"

export function SignUpWithPasswordForm(): JSX.Element {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const [isPending, startTransition] = React.useTransition()

  // Check URL for any error param
  React.useEffect(() => {
    const errorMessage = searchParams.get("error")
    if (errorMessage) {
      toast({
        title: "Registration Error",
        description: decodeURIComponent(errorMessage),
        variant: "destructive",
      })
    }
  }, [searchParams, toast])

  const form = useForm<SignUpWithPasswordFormInput>({
    resolver: zodResolver(signUpWithPasswordSchema),
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
    },
  })

  function onSubmit(formData: SignUpWithPasswordFormInput): void {
    startTransition(async () => {
      try {
        const message = await signUpWithPassword({
          email: formData.email,
          password: formData.password,
          confirmPassword: formData.confirmPassword,
        })

        switch (message) {
          case "exists":
            toast({
              title: "Account already exists",
              description: "Try signing in instead or use a different email",
              variant: "destructive",
            })
            form.setError("email", { 
              message: "Email address already in use" 
            })
            break
          case "success":
            toast({
              title: "Account created!",
              description: "Please check your inbox to verify your email address",
              variant: "default",
            })
            router.push("/signin")
            break
          case "invalid-input":
            toast({
              title: "Invalid information",
              description: "Please check your details and try again",
              variant: "destructive",
            })
            break
          default:
            toast({
              title: "Registration failed",
              description: "Please try again or use another sign up method",
              variant: "destructive",
            })
            console.error(message)
        }
      } catch (error) {
        console.error(error)
        toast({
          title: "Something went wrong",
          description: "Our servers are having issues. Please try again later.",
          variant: "destructive",
        })
      }
    })
  }

  return (
    <Form {...form}>
      <form
        className="grid w-full gap-4"
        onSubmit={(...args) => void form.handleSubmit(onSubmit)(...args)}
      >
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input 
                  type="email"
                  autoComplete="email"
                  placeholder="<EMAIL>" 
                  className="bg-background"
                  {...field} 
                />
              </FormControl>
              <FormMessage className="pt-1 text-xs" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <PasswordInput 
                  placeholder="••••••••" 
                  autoComplete="new-password"
                  className="bg-background"
                  {...field} 
                />
              </FormControl>
              <p className="mt-1 text-xs text-muted-foreground">
                Must include 8+ characters with uppercase, lowercase, number and special character
              </p>
              <FormMessage className="pt-1 text-xs" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Confirm Password</FormLabel>
              <FormControl>
                <PasswordInput 
                  placeholder="••••••••" 
                  autoComplete="new-password"
                  className="bg-background"
                  {...field} 
                />
              </FormControl>
              <FormMessage className="pt-1 text-xs" />
            </FormItem>
          )}
        />

        <Button 
          type="submit"
          disabled={isPending}
          className="mt-2 w-full"
        >
          {isPending ? (
            <>
              <Icons.spinner
                className="mr-2 size-4 animate-spin"
                aria-hidden="true"
              />
              <span>Creating account...</span>
            </>
          ) : (
            <span>Create account</span>
          )}
          <span className="sr-only">
            Continue signing up with email and password
          </span>
        </Button>
      </form>
    </Form>
  )
}
