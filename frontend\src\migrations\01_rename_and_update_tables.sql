-- Rename tailored_applications table to applications for a more generic name
ALTER TABLE tailored_applications RENAME TO applications;

-- Add more status options to the applications table
ALTER TABLE applications DROP CONSTRAINT IF EXISTS applications_status_check;

-- Check for existing status enum types
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tailored_application_status') THEN
        -- If tailored_application_status exists, alter it to add new values if needed
        IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'tailored_application_status') AND enumlabel = 'Partially Completed') THEN
            ALTER TYPE tailored_application_status ADD VALUE 'Partially Completed';
        END IF;
        
        -- Rename type to application_status if it doesn't already exist
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'application_status') THEN
            ALTER TYPE tailored_application_status RENAME TO application_status;
        END IF;
    ELSE
        -- If neither type exists, create application_status
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'application_status') THEN
            CREATE TYPE application_status AS ENUM ('Pending', 'In Progress', 'Partially Completed', 'Completed', 'Failed');
        END IF;
    END IF;
END$$;

-- No need to cast if the column is already the correct type
DO $$
BEGIN
    -- Check the current data type
    IF EXISTS (SELECT 1 FROM information_schema.columns 
              WHERE table_name = 'applications' 
              AND column_name = 'status' 
              AND data_type = 'USER-DEFINED' 
              AND udt_name = 'tailored_application_status') THEN
        -- This will safely convert from tailored_application_status to application_status
        ALTER TABLE applications ALTER COLUMN status TYPE application_status USING status::text::application_status;
    END IF;
END$$;

-- Create documents table to track generated documents
CREATE TABLE IF NOT EXISTS tailored_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    application_id UUID NOT NULL REFERENCES applications(id) ON DELETE CASCADE,
    document_type TEXT NOT NULL CHECK (document_type IN ('resume', 'cover_letter')),
    status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    file_url TEXT,
    content TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    request_id UUID NOT NULL
);

-- Create index on request_id for faster queries
CREATE INDEX IF NOT EXISTS idx_tailored_documents_request_id ON tailored_documents(request_id);

-- Add version tracking for multiple generations of the same document
CREATE TABLE IF NOT EXISTS tailored_document_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES tailored_documents(id) ON DELETE CASCADE,
    version INTEGER NOT NULL,
    file_url TEXT,
    content TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    error_message TEXT
);

-- Create trigger to update updated_at on documents
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tailored_documents_updated_at
    BEFORE UPDATE ON tailored_documents
    FOR EACH ROW
    EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_tailored_document_versions_updated_at
    BEFORE UPDATE ON tailored_document_versions
    FOR EACH ROW
    EXECUTE PROCEDURE update_updated_at_column();
