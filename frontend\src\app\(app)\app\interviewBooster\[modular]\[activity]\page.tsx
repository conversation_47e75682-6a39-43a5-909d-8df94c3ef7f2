"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { AnimatePresence, motion } from "framer-motion"
import {
  ArrowLeft,
  BarChart3,
  CheckCircle,
  ChevronDown,
  ChevronRight,
  ChevronUp,
  Circle,
  Clock,
  Lightbulb,
  MessageSquare,
  Sparkles,
  Target,
} from "lucide-react"

import { useIsMobile } from "@/hooks/use-mobile"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface Question {
  id: string
  section: string
  question: string
  type: "text" | "textarea" | "single-choice" | "yes-no" | "scale"
  options?: string[]
  required?: boolean
  logic?: string
  logic_note?: string
}

export default function ActivityPage() {
  const router = useRouter()
  const isMobile = useIsMobile()
  const [answers, setAnswers] = useState<Record<string, any>>({})
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeQuestion, setActiveQuestion] = useState<string | null>(null)
  const [showChatbot, setShowChatbot] = useState(false)
  const [progress, setProgress] = useState(0)

  // Group questions by section
  const questionsBySection = questions.reduce(
    (acc, question) => {
      if (!acc[question.section]) {
        acc[question.section] = []
      }
      acc[question.section].push(question as Question)
      return acc
    },
    {} as Record<string, Question[]>
  )

  const sections = Object.keys(questionsBySection)

  // Initialize first section as expanded
  useEffect(() => {
    if (sections.length > 0) {
      setExpandedSections({ [sections[0]]: true })

      // Set first question as active
      if (questionsBySection[sections[0]].length > 0) {
        setActiveQuestion(questionsBySection[sections[0]][0].id)
      }
    }
  }, [])

  // Animate progress bar
  useEffect(() => {
    const targetProgress = getTotalProgress()
    const animateProgress = () => {
      setProgress((prev) => {
        if (Math.abs(prev - targetProgress) < 1) return targetProgress
        return prev + (targetProgress > prev ? 1 : -1)
      })
    }

    const interval = setInterval(animateProgress, 20)
    return () => clearInterval(interval)
  }, [answers])

  const handleAnswer = (questionId: string, value: any) => {
    setAnswers((prev) => ({ ...prev, [questionId]: value }))

    // Find next unanswered question
    const allQuestions = questions.map((q) => q.id)
    const currentIndex = allQuestions.indexOf(questionId)

    if (currentIndex < allQuestions.length - 1) {
      const nextQuestion = questions[currentIndex + 1]
      const nextSection = nextQuestion.section

      // Expand the section if needed
      if (!expandedSections[nextSection]) {
        setExpandedSections((prev) => ({
          ...prev,
          [nextSection]: true,
        }))
      }

      // Set next question as active after a short delay
      setTimeout(() => {
        setActiveQuestion(nextQuestion.id)
      }, 300)
    }
  }

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    // Simulate form submission
    setTimeout(() => {
      // onSubmit(answers)
      setIsSubmitting(false)
    }, 2000)
  }

  const getSectionProgress = (section: string) => {
    const sectionQuestions = questionsBySection[section]
    const answeredCount = sectionQuestions.filter(
      (q) => answers[q.id] !== undefined && answers[q.id] !== ""
    ).length
    return (answeredCount / sectionQuestions.length) * 100
  }

  const getTotalProgress = () => {
    const totalQuestions = questions.length
    const answeredCount = questions.filter(
      (q) => answers[q.id] !== undefined && answers[q.id] !== ""
    ).length
    return (answeredCount / totalQuestions) * 100
  }

  const renderQuestion = (question: Question) => {
    const currentAnswer = answers[question.id]
    const isActive = activeQuestion === question.id

    switch (question.type) {
      case "text":
        return (
          <Input
            placeholder="Type your answer here..."
            value={currentAnswer || ""}
            onChange={(e) => handleAnswer(question.id, e.target.value)}
            className="mt-3"
            onFocus={() => setActiveQuestion(question.id)}
          />
        )

      case "textarea":
        return (
          <textarea
            placeholder="Share your thoughts in detail..."
            value={currentAnswer || ""}
            onChange={(e) => handleAnswer(question.id, e.target.value)}
            className="mt-3 min-h-[100px] w-full resize-none rounded-md border p-3 focus:border-purple-500 focus:outline-none"
            onFocus={() => setActiveQuestion(question.id)}
          />
        )

      case "single-choice":
        return (
          <div className="mt-4 space-y-2">
            {question.options?.map((option, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`cursor-pointer rounded-lg border p-3 transition-all hover:bg-gray-50 ${
                  currentAnswer === option
                    ? "border-purple-500 bg-purple-50"
                    : "border-gray-200"
                }`}
                onClick={() => handleAnswer(question.id, option)}
              >
                <div className="flex items-center space-x-3">
                  {currentAnswer === option ? (
                    <CheckCircle className="h-5 w-5 flex-shrink-0 text-purple-500" />
                  ) : (
                    <Circle className="h-5 w-5 flex-shrink-0 text-gray-400" />
                  )}
                  <span className="text-sm">{option}</span>
                </div>
              </motion.div>
            ))}
          </div>
        )

      case "yes-no":
        return (
          <div className="mt-4 flex space-x-4">
            {["Yes", "No"].map((option, index) => (
              <motion.button
                key={option}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => handleAnswer(question.id, option)}
                className={`rounded-lg px-6 py-3 font-medium transition-all ${
                  currentAnswer === option
                    ? option === "Yes"
                      ? "bg-green-500 text-white"
                      : "bg-red-500 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {option}
              </motion.button>
            ))}
          </div>
        )

      case "scale":
        return (
          <div className="mt-4 space-y-4">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>Strongly Disagree</span>
              <span>Strongly Agree</span>
            </div>
            <div className="flex items-center justify-between">
              {[1, 2, 3, 4, 5].map((value, index) => (
                <motion.button
                  key={value}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => handleAnswer(question.id, value)}
                  className={`h-10 w-10 rounded-full border font-semibold transition-all ${
                    currentAnswer === value
                      ? "border-purple-500 bg-purple-500 text-white"
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                >
                  {value}
                </motion.button>
              ))}
            </div>
          </div>
        )

      default:
        return null
    }
  }

  const isQuestionAnswered = (question: Question) => {
    const answer = answers[question.id]
    return answer !== undefined && answer !== ""
  }

  const toggleChatbot = () => {
    setShowChatbot((prev) => !prev)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-purple-50">
      {/* Header */}
      <header className="sticky top-0 z-10 border-b bg-white/90 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              className="flex items-center space-x-2"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Activities</span>
            </Button>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span>10-15 min</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Progress Bar */}
      <div className="border-b bg-white">
        <div className="container mx-auto px-4 py-4">
          <div className="mb-2 flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">
              Overall Progress
            </span>
            <span className="text-sm font-medium text-purple-600">
              {Math.round(progress)}% Complete
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row lg:space-x-8">
          {/* Left Sidebar */}
          <div className="mb-8 lg:mb-0 lg:w-1/4">
            <div className="sticky top-32 space-y-6">
              {/* Activity Header for Mobile */}
              {isMobile && (
                <Card className="mb-8 border-0 bg-gradient-to-r from-purple-500 to-purple-700 text-white shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="text-3xl">🎯</div>
                      <div>
                        <h1 className="text-xl font-bold">
                          Match your skills to the right job title
                        </h1>
                        <p className="mt-2 text-sm text-purple-100">
                          Complete all sections to generate your personalized
                          report
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Navigation */}
              <Card className="border-0 shadow-md">
                <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 pb-3 pt-4">
                  <h3 className="text-md font-semibold text-gray-800">
                    Assessment Sections
                  </h3>
                </CardHeader>
                <CardContent className="p-0">
                  <nav className="divide-y">
                    {sections.map((section, index) => {
                      const sectionProgress = getSectionProgress(section)
                      return (
                        <div
                          key={section}
                          className="cursor-pointer p-3 transition-all hover:bg-gray-50"
                          onClick={() => {
                            toggleSection(section)
                            // Set first unanswered question in section as active
                            const firstUnanswered = questionsBySection[
                              section
                            ].find((q) => !isQuestionAnswered(q))
                            if (firstUnanswered) {
                              setActiveQuestion(firstUnanswered.id)
                            } else if (questionsBySection[section].length > 0) {
                              setActiveQuestion(
                                questionsBySection[section][0].id
                              )
                            }
                          }}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              {expandedSections[section] ? (
                                <ChevronDown className="h-4 w-4 text-purple-500" />
                              ) : (
                                <ChevronRight className="h-4 w-4 text-gray-500" />
                              )}
                              <span className="text-sm font-medium">
                                {section}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-gray-500">
                                {Math.round(sectionProgress)}%
                              </span>
                              <div className="h-1.5 w-12 rounded-full bg-gray-200">
                                <div
                                  className={`h-1.5 rounded-full ${
                                    sectionProgress === 100
                                      ? "bg-green-500"
                                      : "bg-purple-500"
                                  }`}
                                  style={{ width: `${sectionProgress}%` }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </nav>
                </CardContent>
              </Card>

              {/* Stats Card */}
              <Card className="border-0 shadow-md">
                <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 pb-3 pt-4">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="h-4 w-4 text-purple-500" />
                    <h3 className="text-md font-semibold text-gray-800">
                      Your Progress
                    </h3>
                  </div>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    <div>
                      <div className="mb-1 flex items-center justify-between">
                        <span className="text-xs text-gray-500">
                          Questions Answered
                        </span>
                        <span className="text-xs font-medium text-purple-600">
                          {
                            questions.filter((q) =>
                              isQuestionAnswered(q as Question)
                            ).length
                          }
                          /{questions.length}
                        </span>
                      </div>
                      <Progress value={getTotalProgress()} className="h-1.5" />
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="rounded-lg bg-purple-50 p-3 text-center">
                        <div className="text-lg font-bold text-purple-700">
                          {Math.round(getTotalProgress())}%
                        </div>
                        <div className="text-xs text-gray-500">Completion</div>
                      </div>
                      <div className="rounded-lg bg-green-50 p-3 text-center">
                        <div className="text-lg font-bold text-green-700">
                          {
                            sections.filter(
                              (section) => getSectionProgress(section) === 100
                            ).length
                          }
                        </div>
                        <div className="text-xs text-gray-500">
                          Sections Done
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Activity Header for Desktop */}
              {!isMobile && (
                <Card className="border-0 bg-gradient-to-r from-purple-500 to-purple-700 text-white shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="text-3xl">🎯</div>
                      <div>
                        <h1 className="text-xl font-bold">
                          Match your skills to the right job title
                        </h1>
                        <p className="mt-2 text-sm text-purple-100">
                          Complete all sections to generate your personalized
                          report
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:w-2/4">
            <div className="space-y-6">
              {sections.map((section) => {
                const isExpanded = expandedSections[section]

                return (
                  <Card
                    key={section}
                    className="overflow-hidden border-0 shadow-md"
                  >
                    <CardHeader
                      className="cursor-pointer bg-gradient-to-r from-gray-50 to-gray-100 transition-all hover:from-gray-100 hover:to-gray-200"
                      onClick={() => toggleSection(section)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            {isExpanded ? (
                              <ChevronDown className="h-5 w-5 text-purple-600" />
                            ) : (
                              <ChevronRight className="h-5 w-5 text-gray-600" />
                            )}
                            <h3 className="text-lg font-semibold text-gray-800">
                              {section}
                            </h3>
                          </div>
                          <Badge
                            variant={
                              getSectionProgress(section) === 100
                                ? "default"
                                : "secondary"
                            }
                            className={
                              getSectionProgress(section) === 100
                                ? "bg-green-500"
                                : ""
                            }
                          >
                            {questionsBySection[section].length} questions
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className="text-sm text-gray-600">
                            {Math.round(getSectionProgress(section))}% complete
                          </span>
                          <div className="h-2 w-24 rounded-full bg-gray-200">
                            <div
                              className={`h-2 rounded-full transition-all duration-300 ${
                                getSectionProgress(section) === 100
                                  ? "bg-green-500"
                                  : "bg-purple-500"
                              }`}
                              style={{
                                width: `${getSectionProgress(section)}%`,
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </CardHeader>

                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <CardContent className="p-6">
                            <div className="space-y-8">
                              {questionsBySection[section].map(
                                (question, questionIndex) => {
                                  const isActive =
                                    activeQuestion === question.id
                                  const isAnswered =
                                    isQuestionAnswered(question)

                                  return (
                                    <motion.div
                                      key={question.id}
                                      initial={{ opacity: 0, y: 20 }}
                                      animate={{
                                        opacity: 1,
                                        y: 0,
                                        scale: isActive ? 1.02 : 1,
                                      }}
                                      transition={{
                                        delay: questionIndex * 0.1,
                                        duration: 0.3,
                                      }}
                                      className={`rounded-lg border p-6 transition-all ${
                                        isAnswered
                                          ? "border-green-200 bg-green-50"
                                          : isActive
                                            ? "border-purple-300 bg-purple-50 shadow-md"
                                            : "border-gray-200 bg-white"
                                      }`}
                                      onClick={() =>
                                        setActiveQuestion(question.id)
                                      }
                                    >
                                      <div className="flex items-start space-x-4">
                                        <div
                                          className={`mt-1 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full ${
                                            isAnswered
                                              ? "bg-green-500 text-white"
                                              : isActive
                                                ? "bg-purple-500 text-white"
                                                : "bg-gray-100 text-gray-600"
                                          }`}
                                        >
                                          {isAnswered ? (
                                            <CheckCircle className="h-4 w-4" />
                                          ) : (
                                            <span className="text-sm font-medium">
                                              {questionIndex + 1}
                                            </span>
                                          )}
                                        </div>
                                        <div className="flex-1">
                                          <h4 className="mb-2 text-lg font-medium text-gray-800">
                                            {question.question}
                                            {question.required && (
                                              <span className="ml-1 text-red-500">
                                                *
                                              </span>
                                            )}
                                          </h4>
                                          {renderQuestion(question)}

                                          {question.logic && (
                                            <div className="mt-4 flex items-center space-x-2 text-xs text-gray-500">
                                              <TooltipProvider>
                                                <Tooltip>
                                                  <TooltipTrigger asChild>
                                                    <div className="flex cursor-help items-center space-x-1">
                                                      <Lightbulb className="h-3 w-3" />
                                                      <span>
                                                        Logic: {question.logic}
                                                      </span>
                                                    </div>
                                                  </TooltipTrigger>
                                                  <TooltipContent>
                                                    <p className="max-w-xs text-xs">
                                                      {question.logic_note}
                                                    </p>
                                                  </TooltipContent>
                                                </Tooltip>
                                              </TooltipProvider>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </motion.div>
                                  )
                                }
                              )}
                            </div>
                          </CardContent>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Card>
                )
              })}
            </div>

            {/* Submit Button */}
            <motion.div
              className="mt-8 flex justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <Button
                onClick={handleSubmit}
                disabled={getTotalProgress() < 80 || isSubmitting}
                className="bg-gradient-to-r from-purple-600 to-purple-800 px-8 py-6 text-lg hover:from-purple-700 hover:to-purple-900"
              >
                {isSubmitting ? (
                  <div className="flex items-center space-x-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                    <span>Generating Report...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Sparkles className="h-5 w-5" />
                    <span>{`Generate Report (${Math.round(getTotalProgress())}% complete)`}</span>
                  </div>
                )}
              </Button>
            </motion.div>
          </div>

          {/* Right Sidebar - Future Chatbot Space */}
          <div className="mt-8 lg:mt-0 lg:w-1/4">
            <div className="sticky top-32">
              <Card className="border-0 shadow-md">
                <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 pb-3 pt-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <MessageSquare className="h-4 w-4 text-purple-500" />
                      <h3 className="text-md font-semibold text-gray-800">
                        AI Assistant
                      </h3>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={toggleChatbot}
                    >
                      {showChatbot ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronUp className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardHeader>

                <AnimatePresence>
                  {showChatbot && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                    >
                      <CardContent className="p-4">
                        <div className="flex h-[400px] flex-col rounded-lg border bg-gray-50 p-4">
                          <div className="flex-1 space-y-4 overflow-y-auto">
                            <div className="flex justify-start">
                              <div className="max-w-[80%] rounded-lg bg-white p-3 shadow-sm">
                                <p className="text-sm text-gray-800">
                                  Hi there! I&apos;m your AI assistant. I can
                                  help you complete this assessment. Do you have
                                  any questions about the form?
                                </p>
                              </div>
                            </div>

                            <div className="flex justify-end">
                              <div className="max-w-[80%] rounded-lg bg-purple-100 p-3 shadow-sm">
                                <p className="text-sm text-gray-800">
                                  This is a placeholder for the future chatbot
                                  integration.
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="mt-4 flex items-center space-x-2">
                            <Input
                              placeholder="Ask me anything about the assessment..."
                              className="flex-1"
                              disabled
                            />
                            <Button size="sm" disabled>
                              Send
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </motion.div>
                  )}
                </AnimatePresence>

                {!showChatbot && (
                  <CardContent className="p-4">
                    <div className="rounded-lg bg-purple-50 p-4 text-center">
                      <p className="text-sm text-gray-600">
                        Click to expand the AI assistant that can help you
                        complete this assessment.
                      </p>
                    </div>
                  </CardContent>
                )}
              </Card>

              {/* Tips Card */}
              <Card className="mt-6 border-0 shadow-md">
                <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 pb-3 pt-4">
                  <div className="flex items-center space-x-2">
                    <Target className="h-4 w-4 text-purple-500" />
                    <h3 className="text-md font-semibold text-gray-800">
                      Assessment Tips
                    </h3>
                  </div>
                </CardHeader>
                <CardContent className="p-4">
                  <ul className="space-y-3 text-sm text-gray-600">
                    <li className="flex items-start space-x-2">
                      <CheckCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-500" />
                      <span>Answer honestly for the most accurate results</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <CheckCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-500" />
                      <span>
                        Complete all sections to get a comprehensive report
                      </span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <CheckCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-500" />
                      <span>
                        Use the AI assistant if you need help understanding
                        questions
                      </span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Action Button for Mobile */}
      {isMobile && (
        <div className="fixed bottom-6 right-6 z-20">
          <Button
            onClick={toggleChatbot}
            className="h-14 w-14 rounded-full bg-purple-600 p-0 shadow-lg hover:bg-purple-700"
          >
            <MessageSquare className="h-6 w-6" />
          </Button>
        </div>
      )}
    </div>
  )
}

const questions = [
  {
    id: "q1",
    section: "Application Volume",
    question: "When did you start a serious job search journey?",
    type: "single-choice",
    options: [
      "Less than 1 month ago",
      "3 months ago",
      "6 months ago",
      "9 months ago",
      "More than 9 months",
    ],
    logic: "A",
    logic_note:
      "Used with Q2 to determine if total volume is too low given search time",
  },
  {
    id: "q2",
    section: "Application Volume",
    question: "How many positions have you submitted so far?",
    type: "single-choice",
    options: ["Less than 200", "200–500", "501–1000", "1001+"],
    logic: "A",
    logic_note: "Combined with Q1 to analyze total volume",
  },
  {
    id: "q3",
    section: "Application Volume",
    question: "How many applications have you submitted in the last 30 days?",
    type: "single-choice",
    options: ["0", "1–60", "61–150", "More than 150"],
    logic: "B",
    logic_note: "Used to compute average daily volume",
  },
  {
    id: "q4",
    section: "Application Volume",
    question:
      "How many OAs (online assessments) did you receive from all your applications?",
    type: "text",
    logic: "C",
    logic_note: "Used to estimate OA rate vs application count",
  },
  {
    id: "q5",
    section: "Application Strategy",
    question:
      "80% of my applications were submitted within 72 hours of the job postings.",
    type: "yes-no",
    logic: "D",
    logic_note: "Tests urgency and timing of submissions",
  },
  {
    id: "q6",
    section: "Application Strategy",
    question: "When applying for jobs, I usually:",
    type: "single-choice",
    options: [
      "First check the posting time of the position",
      "Don't pay much attention to the posting time before applying",
      "Don't have a fixed habit—sometimes I check, sometimes I don't",
    ],
    logic: "E",
    logic_note: "Assesses submission habits and consistency",
  },
  {
    id: "q7",
    section: "Application Strategy",
    question:
      "I am using Excel or any other application tracking system to record my job hunting.",
    type: "yes-no",
    logic: "F",
    logic_note: "Tracks habit of maintaining job search records",
  },
  {
    id: "q8",
    section: "Application Strategy",
    question:
      "I clearly know how to use an application tracking system to assist my job hunting journey.",
    type: "yes-no",
    logic: "G",
    logic_note: "Assesses skill level in using tracking systems effectively",
  },
  {
    id: "q9",
    section: "Application Strategy",
    question:
      "I often use and check 3 different platforms (or more) to apply jobs to ensure a wide range of sources.",
    type: "yes-no",
    logic: "H",
    logic_note: "Evaluates diversity of sourcing channels",
  },
  {
    id: "q10",
    section: "Application Strategy",
    question:
      "70+% of my applications target the industry where I have enough domain knowledge or industrial experience.",
    type: "yes-no",
    logic: "I",
    logic_note: "Tests alignment of application with user's background",
  },
  {
    id: "q11",
    section: "Application Strategy",
    question:
      "I know the importance of networking and am open to doing cold-start networking to expand my network.",
    type: "yes-no",
  },
  {
    id: "q12",
    section: "Application Strategy",
    question:
      "I have already done some cold-start networking on LinkedIn or any other events.",
    type: "yes-no",
    logic: "K",
    logic_note: "Tests networking action vs just awareness",
  },
  {
    id: "q13",
    section: "Application Strategy",
    question:
      "I have already taken actions on seeking valuable information, resume reviewing, or career assistance from professional contacts.",
    type: "yes-no",
  },
  {
    id: "q14",
    section: "Application Strategy",
    question:
      "How many people from your own network have provided internal referrals for you?",
    type: "text",
    logic: "N",
    logic_note:
      "Used in combination with Q15 to assess networking effectiveness",
  },
  {
    id: "q15",
    section: "Application Strategy",
    question:
      "I clearly know the correct way of communicating with different types of new connections, which means I'm very familiar with basic networking processes.",
    type: "yes-no",
    logic: "P",
    logic_note: "Measures networking communication competency",
  },
]
