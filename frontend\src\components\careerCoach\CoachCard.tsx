"use client"

import { useMemo } from "react"
import { type Coach } from "@/services/careerCoachService"
import { ArrowRight, Briefcase, CheckCircle, User, Users } from "lucide-react"

import { cn } from "@/lib/utils"

import { Button } from "@/components/ui/button"

// Focused messaging for each coach type
const coachProfiles = {
  career: {
    tagline: "Find Your True Calling",
    targetAudience: "Career Changers & New Grads",
    keyBenefit:
      "Discover the perfect career path aligned with your personality and interests",
    highlights: ["Personality Assessment", "Career Matching", "Path Planning"],
  },
  resume: {
    tagline: "Land More Interviews",
    targetAudience: "Job Seekers & Career Switchers",
    keyBenefit:
      "Get past ATS systems and impress hiring managers with optimized resumes",
    highlights: [
      "ATS Optimization",
      "Achievement Highlighting",
      "Industry Formatting",
    ],
  },
  linkedin: {
    tagline: "Build Your Personal Brand",
    targetAudience: "Professionals & Networkers",
    keyBenefit:
      "Stand out to recruiters and build a powerful professional presence online",
    highlights: [
      "Profile Optimization",
      "Recruiter Visibility",
      "Personal Branding",
    ],
  },
}

// More elegant and professional color scheme
const coachIconMap = [
  {
    key: "career",
    icon: Briefcase,
    color: "text-slate-700",
    bg: "bg-slate-50",
    border: "border-slate-200",
    badge: "bg-slate-100 text-slate-700",
    accent: "slate",
  },
  {
    key: "resume",
    icon: User,
    color: "text-indigo-700",
    bg: "bg-indigo-50",
    border: "border-indigo-200",
    badge: "bg-indigo-100 text-indigo-700",
    accent: "indigo",
  },
  {
    key: "linkedin",
    icon: Users,
    color: "text-violet-700",
    bg: "bg-violet-50",
    border: "border-violet-200",
    badge: "bg-violet-100 text-violet-700",
    accent: "violet",
  },
]

export default function CoachCard({ coach }: { coach: Coach }) {
  // Determine coach type and styling
  const {
    Icon,
    colorClass,
    bgClass,
    borderClass,
    badgeClass,
    accent,
    profile,
  } = useMemo(() => {
    let coachType = "career"

    if (coach.specialty.toLowerCase().includes("resume")) {
      coachType = "resume"
    } else if (coach.specialty.toLowerCase().includes("linkedin")) {
      coachType = "linkedin"
    }

    const styleConfig =
      coachIconMap.find(({ key }) => key === coachType) || coachIconMap[0]

    return {
      Icon: styleConfig.icon,
      colorClass: styleConfig.color,
      bgClass: styleConfig.bg,
      borderClass: styleConfig.border,
      badgeClass: styleConfig.badge,
      accent: styleConfig.accent,
      profile: coachProfiles[coachType as keyof typeof coachProfiles],
    }
  }, [coach.specialty])

  return (
    <div
      className={cn(
        "group relative flex h-full flex-col overflow-hidden rounded-xl border bg-white shadow-sm transition-all duration-300 hover:shadow-md",
        borderClass,
        "cursor-pointer hover:-translate-y-1"
      )}
    >
      <div className="flex flex-1 flex-col p-6">
        {/* Header */}
        <div className="mb-6 flex items-start gap-4">
          <div
            className={cn(
              "rounded-lg p-3 transition-transform duration-300 group-hover:scale-105",
              bgClass
            )}
          >
            <Icon className={cn("size-5", colorClass)} />
          </div>

          <div className="flex-1">
            <h3 className="mb-2 text-xl font-semibold text-slate-900 transition-colors group-hover:text-slate-700">
              {coach.name}
            </h3>
            <div
              className={cn(
                "inline-flex items-center rounded-full px-3 py-1 text-xs font-medium",
                badgeClass
              )}
            >
              {profile.targetAudience}
            </div>
          </div>
        </div>

        {/* Tagline */}
        <div className="mb-4">
          <h4 className={cn("mb-2 text-lg font-semibold", colorClass)}>
            {profile.tagline}
          </h4>
          <p className="text-sm leading-relaxed text-slate-600">
            {profile.keyBenefit}
          </p>
        </div>

        {/* Key Highlights */}
        <div className="mb-6 flex-1">
          <div className="space-y-2">
            {profile.highlights.map((highlight, idx) => (
              <div key={idx} className="flex items-center gap-2">
                <CheckCircle className="size-3 shrink-0 text-emerald-500" />
                <span className="text-xs text-slate-600">{highlight}</span>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Button - more professional styling */}
        <Button
          asChild
          className={cn(
            "w-full rounded-lg py-5 font-medium transition-all duration-200 group-hover:scale-[1.02]",
            "border-2 bg-white text-slate-700 shadow-sm hover:bg-slate-50 hover:shadow",
            accent === "slate" && "border-slate-300 hover:border-slate-400",
            accent === "indigo" &&
              "border-indigo-300 hover:border-indigo-400 hover:text-indigo-700",
            accent === "violet" &&
              "border-violet-300 hover:border-violet-400 hover:text-violet-700"
          )}
        >
          <a
            href={`/app/careerCoach/${coach.id}`}
            className="flex items-center justify-center gap-2"
          >
            Choose {coach.name.split(" ")[0]}
            <ArrowRight className="size-4 transition-transform group-hover:translate-x-1" />
          </a>
        </Button>
      </div>
    </div>
  )
}
