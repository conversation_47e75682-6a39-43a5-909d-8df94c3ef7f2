{"endOfLine": "lf", "semi": false, "singleQuote": false, "tabWidth": 2, "trailingComma": "es5", "importOrder": ["^@/styles/(.*)$", "", "^(react/(.*)$)|^(react$)", "^(next/(.*)$)|^(next$)", "<THIRD_PARTY_MODULES>", "", "^@/env.mjs", "^types$", "^@/types/(.*)$", "^@/config/(.*)$", "^@/db/(.*)$", "^@/validations/(.*)$", "^@/data/(.*)$", "", "^@/providers/(.*)$", "^@/hooks/(.*)$", "^@/lib/(.*)$", "", "^@/components/ui/(.*)$", "^@/components/(.*)$", "^@/app/(.*)$", "", "^[./]"], "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "tailwindAttributes": ["tw"], "tailwindFunctions": ["cva"], "plugins": ["@ianvs/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"]}