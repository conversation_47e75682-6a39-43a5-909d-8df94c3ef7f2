"use client"

import { useEffect, useState, useRef } from "react"
import { Send, Bot, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { BASE_API_URL } from "@/config/apiEndpoints"

interface ChatMessage {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
}

interface CopilotChatInterfaceProps {
  coachId: string
  sessionId: string
  userId: string
  coachName: string
  coachSpecialty: string
  onMessageSent?: (message: string) => void
}

export default function CopilotChatInterface({
  coachId,
  sessionId,
  userId,
  coachName,
  coachSpecialty,
  onMessageSent,
}: CopilotChatInterfaceProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputMessage, setInputMessage] = useState("")
  const [isConnected, setIsConnected] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [currentResponse, setCurrentResponse] = useState("")
  const wsRef = useRef<WebSocket | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, currentResponse])

  useEffect(() => {
    // Initialize with welcome message
    const welcomeMessage: ChatMessage = {
      id: "welcome",
      role: "assistant",
      content: `Hello! 👋 I'm ${coachName}, your ${coachSpecialty} coach. How can I help you today?`,
      timestamp: new Date(),
    }
    setMessages([welcomeMessage])

    // Set up WebSocket connection
    const wsUrl = BASE_API_URL.replace("http://", "ws://").replace("https://", "wss://")
    const ws = new WebSocket(`${wsUrl}/career-coach/ws/chat`)

    ws.onopen = () => {
      setIsConnected(true)
      console.log("WebSocket connected")
    }

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data)

      if (data.error) {
        console.error("WebSocket error:", data.error)
        return
      }

      if (data.streaming === true) {
        setIsTyping(true)
        setCurrentResponse("")
      } else if (data.chunk) {
        setCurrentResponse(prev => prev + data.chunk)
      } else if (data.streaming === false && data.response) {
        setIsTyping(false)
        const assistantMessage: ChatMessage = {
          id: `assistant_${Date.now()}`,
          role: "assistant",
          content: data.response,
          timestamp: new Date(),
        }
        setMessages(prev => [...prev, assistantMessage])
        setCurrentResponse("")
      }
    }

    ws.onclose = () => {
      setIsConnected(false)
      console.log("WebSocket disconnected")
    }

    ws.onerror = (error) => {
      console.error("WebSocket error:", error)
      setIsConnected(false)
    }

    wsRef.current = ws

    return () => {
      ws.close()
    }
  }, [coachName, coachSpecialty])

  const handleSendMessage = () => {
    if (!inputMessage.trim() || !isConnected || isTyping) return

    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      role: "user",
      content: inputMessage,
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    onMessageSent?.(inputMessage)

    // Send message via WebSocket
    if (wsRef.current) {
      wsRef.current.send(JSON.stringify({
        message: inputMessage,
        coach_id: coachId,
        user_id: userId,
        session_id: sessionId,
      }))
    }

    setInputMessage("")
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  return (
    <div className="flex h-full flex-col bg-gray-50 overflow-hidden">
      {/* Connection Status */}
      {!isConnected && (
        <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2 flex-shrink-0">
          <p className="text-sm text-yellow-800">Connecting to chat...</p>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
          >
            <div className="flex items-start gap-3 max-w-xs lg:max-w-md xl:max-w-lg">
              {message.role === "assistant" && (
                <div className="flex size-8 items-center justify-center rounded-full bg-blue-100 mt-1 shrink-0">
                  <Bot className="size-4 text-blue-600" />
                </div>
              )}
              <div
                className={`rounded-2xl px-4 py-3 ${
                  message.role === "user"
                    ? "bg-blue-600 text-white rounded-tr-md"
                    : "bg-white text-gray-900 border border-gray-200 rounded-tl-md"
                }`}
              >
                {message.role === "assistant" && (
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-xs font-medium text-gray-700">{coachName}</span>
                    <span className="text-xs text-gray-500">{formatTime(message.timestamp)}</span>
                  </div>
                )}
                <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
                {message.role === "user" && (
                  <p className="text-xs text-blue-100 mt-2">{formatTime(message.timestamp)}</p>
                )}
              </div>
              {message.role === "user" && (
                <div className="flex size-8 items-center justify-center rounded-full bg-blue-100 mt-1 shrink-0">
                  <User className="size-4 text-blue-600" />
                </div>
              )}
            </div>
          </div>
        ))}

        {/* Typing indicator with streaming response */}
        {isTyping && (
          <div className="flex justify-start">
            <div className="flex items-start gap-3 max-w-xs lg:max-w-md xl:max-w-lg">
              <div className="flex size-8 items-center justify-center rounded-full bg-blue-100 mt-1 shrink-0">
                <Bot className="size-4 text-blue-600" />
              </div>
              <div className="bg-white border border-gray-200 rounded-2xl rounded-tl-md px-4 py-3">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-xs font-medium text-gray-700">{coachName}</span>
                  <span className="text-xs text-gray-500">typing...</span>
                </div>
                <div className="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">
                  {currentResponse}
                  <span className="inline-block w-2 h-4 bg-blue-600 animate-pulse ml-1"></span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="border-t bg-white p-4 flex-shrink-0">
        <div className="flex gap-3">
          <Textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Type your message here..."
            className="flex-1 min-h-[44px] max-h-32 resize-none"
            rows={1}
            disabled={!isConnected || isTyping}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || !isConnected || isTyping}
            className="bg-blue-600 hover:bg-blue-700 px-6"
          >
            <Send className="size-4" />
          </Button>
        </div>
        <p className="mt-2 text-center text-xs text-gray-500">
          Press Enter to send, Shift+Enter for new line
        </p>
      </div>
    </div>
  )
}
