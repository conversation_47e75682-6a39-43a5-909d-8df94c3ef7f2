# AWS Deployment Guide for SophieAI

This guide outlines the process for deploying the SophieAI application to AWS, using a production-grade, cost-efficient approach.

## Architecture Overview

Our deployment architecture consists of:

1. **Frontend**: AWS Amplify
2. **Backend**: Amazon ECR + AWS App Runner

![Deployment Architecture](https://i.imgur.com/bxNrDOE.png)

### Why this architecture?

- **AWS Amplify** provides a fully managed hosting service with CI/CD, global CDN, and automatic scaling, perfect for modern web applications.
- **AWS App Runner** offers a fully managed service for containerized applications with automatic scaling, making it simpler than Fargate for REST API services.
- This combination provides a good balance of automation, production readiness, and cost efficiency.

## Prerequisites

- AWS CLI installed and configured with appropriate permissions
- Docker installed for building container images
- Access to your AWS account with permissions for:
  - ECR (Amazon Elastic Container Registry)
  - App Runner
  - Amplify
  - IAM (for creating service roles)

## Initial Setup

### 1. Configure AWS CLI

```bash
aws configure
```

### 2. Create an Amplify App (Manual Step)

1. Navigate to the Amplify Console in your AWS account
2. Click "Create app" → "Host web app"
3. Connect your repository (GitHub, GitLab, Bitbucket, or AWS CodeCommit)
4. Configure build settings for your frontend framework
5. Deploy the app
6. Note the `AMPLIFY_APP_ID` for use in the Makefile

## Using the Deployment Tools

The deployment tools in this directory provide a streamlined process for deploying both frontend and backend components.

### Configuration

Edit the variables at the top of the `Makefile` or override them using environment variables:

```bash
# Required
export AMPLIFY_APP_ID=your-amplify-app-id

# Optional - defaults provided in Makefile
export AWS_REGION=us-east-1
export ECR_REPO_NAME=sophieai-backend
export APP_RUNNER_SERVICE_NAME=sophieai-backend-service
export BACKEND_IMAGE_TAG=latest
export BRANCH_NAME=main
```

### Available Commands

| Command | Description |
|---------|-------------|
| `make setup` | Initial setup of AWS resources (ECR repository) |
| `make build-backend` | Build the backend Docker image |
| `make push-backend` | Push the backend image to ECR (includes build) |
| `make deploy-backend` | Deploy backend to App Runner (includes build and push) |
| `make deploy-frontend` | Deploy frontend to AWS Amplify |
| `make deploy` | Deploy both frontend and backend |
| `make clean` | Clean up local build artifacts |
| `make status` | Check deployment status |

## Deployment Workflow

### First-time Deployment

```bash
# 1. Set up AWS resources
make setup

# 2. Build and deploy the backend
make deploy-backend

# 3. Deploy the frontend
make deploy-frontend
```

### Subsequent Deployments

```bash
# Full deployment
make deploy

# Or deploy components separately
make deploy-backend
make deploy-frontend
```

## Cost Optimization Recommendations

### General Cost-Saving Tips

1. **Use AWS Free Tier**: Both Amplify and App Runner have generous free tiers
2. **Enable Auto-Scaling**: Configured for App Runner to scale based on demand
3. **Choose Optimal Instance Sizes**: App Runner is configured with 1vCPU/2GB, adjust as needed
4. **Set Max Instances**: App Runner config limits max instances to 10 by default

### AWS Amplify Cost Optimization

1. **Limit Build Minutes**: Optimize your build process to reduce build minutes
2. **Consider Monorepo Approach**: Deploy multiple apps from a single repo to save on build costs
3. **Implement Preview Branches Wisely**: Limit the number of preview branches to reduce costs

### AWS App Runner Cost Optimization

1. **Use Hibernation**: Consider using the AutoPause feature for non-production environments
2. **Optimize Container Size**: Keep your Docker images small to reduce resource consumption
3. **Set Appropriate Concurrency**: Configure container concurrency based on your application's needs

## Monitoring and Alerts

Consider setting up:

1. **CloudWatch Alarms**: For high cost alerts
2. **Budget Alerts**: To get notified when spending exceeds thresholds
3. **Cost Explorer**: To regularly analyze your spending patterns

## Troubleshooting

### Common Issues

1. **Deployment Fails**: Check AWS permissions and service quotas
2. **Script Execution Permissions**: Ensure scripts are executable with `chmod +x scripts/*.sh`
3. **AWS CLI Authentication**: Verify credentials with `aws sts get-caller-identity`

For detailed logs, check:
- AWS App Runner service logs in CloudWatch
- AWS Amplify build logs in the Amplify Console

## Advanced Customization

1. **Custom Domain Names**: Configure via AWS Amplify and App Runner console
2. **Environment Variables**: Configure in the respective service consoles or through CI/CD
3. **SSL Certificates**: Both services handle SSL automatically with AWS managed certificates
