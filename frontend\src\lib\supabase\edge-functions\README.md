# Supabase Edge Functions

This directory contains the Edge Functions used in the application.

## Deployment Instructions

To deploy the edge functions to Supabase, follow these steps:

### Prerequisites

1. Install Supabase CLI if not already installed:
   ```bash
   npm install -g supabase
   ```

2. Login to Supabase CLI:
   ```bash
   supabase login
   ```

### Deploy Edge Function

For deploying the `process-tailoring-request` edge function:

1. Navigate to the project root directory:
   ```bash
   cd /path/to/project
   ```

2. Link to your Supabase project (replace `<project-ref>` with your Supabase project reference):
   ```bash
   supabase link --project-ref <project-ref>
   ```

3. Deploy the edge function:
   ```bash
   supabase functions deploy process-tailoring-request
   ```

4. Set environment variables needed by the edge function:
   ```bash
   supabase secrets set MATERIAL_GENERATOR_API_URL=<your-api-url>
   ```

### Testing the Edge Function

You can test the edge function after deployment:

```bash
supabase functions invoke process-tailoring-request --body '{"applicationId":"123", "resumeId":"456", "jobDescription":"Sample job", "companyName":"Company", "jobTitle":"Title", "generateResume":true, "generateCoverLetter":true, "requestId":"789"}'
```

### Troubleshooting

If you're experiencing 404 errors when calling the edge function:

1. Verify that the function is deployed correctly:
   ```bash
   supabase functions list
   ```

2. Check the deployment logs:
   ```bash
   supabase functions logs
   ```

3. Ensure the function name in your code matches exactly with the deployed function name
