import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Thum<PERSON><PERSON><PERSON>, ThumbsDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface ChatMessageProps {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
  coachName?: string
  onFeedback?: (messageId: string, feedback: "positive" | "negative") => void
}

export default function ChatMessage({
  id,
  content,
  role,
  timestamp,
  coachName,
  onFeedback,
}: ChatMessageProps) {
  const [copied, setCopied] = useState(false)
  const [feedback, setFeedback] = useState<"positive" | "negative" | null>(null)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error("Failed to copy text:", err)
    }
  }

  const handleFeedback = (type: "positive" | "negative") => {
    setFeedback(type)
    onFeedback?.(id, type)
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  if (role === "user") {
    return (
      <div className="flex justify-end mb-4">
        <div className="flex items-start gap-3 max-w-xs lg:max-w-md xl:max-w-lg">
          <div className="bg-blue-600 text-white rounded-2xl rounded-tr-md px-4 py-3 shadow-sm">
            <p className="text-sm whitespace-pre-wrap">{content}</p>
            <p className="text-xs text-blue-100 mt-2">{formatTime(timestamp)}</p>
          </div>
          <div className="flex size-8 items-center justify-center rounded-full bg-blue-100 mt-1">
            <User className="size-4 text-blue-600" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex justify-start mb-4">
      <div className="flex items-start gap-3 max-w-xs lg:max-w-md xl:max-w-lg">
        <div className="flex size-8 items-center justify-center rounded-full bg-gray-100 mt-1 shrink-0">
          <Bot className="size-4 text-gray-600" />
        </div>
        <div className="bg-white border border-gray-200 rounded-2xl rounded-tl-md px-4 py-3 shadow-sm">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-xs font-medium text-gray-700">
              {coachName || "Career Coach"}
            </span>
            <span className="text-xs text-gray-500">{formatTime(timestamp)}</span>
          </div>
          <p className="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">
            {content}
          </p>
          
          {/* Message Actions */}
          <div className="flex items-center gap-2 mt-3 pt-2 border-t border-gray-100">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopy}
              className="h-7 px-2 text-xs text-gray-500 hover:text-gray-700"
            >
              {copied ? (
                <>
                  <Check className="size-3 mr-1" />
                  Copied
                </>
              ) : (
                <>
                  <Copy className="size-3 mr-1" />
                  Copy
                </>
              )}
            </Button>
            
            {onFeedback && (
              <div className="flex items-center gap-1 ml-auto">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleFeedback("positive")}
                  className={cn(
                    "h-7 px-2 text-xs",
                    feedback === "positive"
                      ? "text-green-600 bg-green-50"
                      : "text-gray-500 hover:text-green-600"
                  )}
                >
                  <ThumbsUp className="size-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleFeedback("negative")}
                  className={cn(
                    "h-7 px-2 text-xs",
                    feedback === "negative"
                      ? "text-red-600 bg-red-50"
                      : "text-gray-500 hover:text-red-600"
                  )}
                >
                  <ThumbsDown className="size-3" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
