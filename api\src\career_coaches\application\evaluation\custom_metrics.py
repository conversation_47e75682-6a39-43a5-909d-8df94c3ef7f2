"""
Custom evaluation metrics for career coaching effectiveness.
"""

import asyncio
from typing import Dict, Any, List, Optional
from opik.evaluation.metrics import BaseMetric
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from loguru import logger


class ActionabilityMetric(BaseMetric):
    """Evaluates how actionable and practical the career coaching advice is."""
    
    name = "actionability"
    
    def __init__(self, model_name: str = "gpt-4o-mini"):
        self.model = ChatOpenAI(model=model_name, temperature=0.0)
        
        self.evaluation_prompt = ChatPromptTemplate.from_template("""
        You are an expert career coach evaluator. Assess how actionable and practical the coaching advice is.

        User Query: {input}
        Coach Response: {output}

        Evaluate the actionability of the coaching advice on a scale of 0.0 to 1.0 based on:
        1. Specific, concrete steps provided
        2. Clear timeline or sequence of actions
        3. Realistic and achievable recommendations
        4. Practical tools or resources mentioned
        5. Measurable outcomes or milestones

        Provide your score as a float between 0.0 and 1.0, where:
        - 0.0-0.3: Vague advice with no clear action steps
        - 0.4-0.6: Some actionable elements but lacks specificity
        - 0.7-0.9: Clear, specific, and practical advice
        - 1.0: Exceptionally detailed and actionable guidance

        Score: [Your score as a float]
        Reasoning: [Brief explanation of your scoring]
        """)
    
    async def ascore(self, input: str, output: str, **kwargs) -> float:
        """Asynchronously score the actionability of coaching advice."""
        try:
            chain = self.evaluation_prompt | self.model
            result = await chain.ainvoke({
                "input": input,
                "output": output
            })
            
            # Extract score from response
            response_text = result.content
            score_line = [line for line in response_text.split('\n') if line.startswith('Score:')]
            
            if score_line:
                score_str = score_line[0].replace('Score:', '').strip()
                score = float(score_str)
                return max(0.0, min(1.0, score))  # Ensure score is between 0 and 1
            
            return 0.5  # Default score if parsing fails
            
        except Exception as e:
            logger.error(f"Error in ActionabilityMetric: {e}")
            return 0.5

    def score(self, input: str, output: str, **kwargs) -> float:
        """Synchronously score the actionability of coaching advice."""
        return asyncio.run(self.ascore(input, output, **kwargs))


class EmpathyMetric(BaseMetric):
    """Evaluates how empathetic and supportive the coaching response is."""
    
    name = "empathy"
    
    def __init__(self, model_name: str = "gpt-4o-mini"):
        self.model = ChatOpenAI(model=model_name, temperature=0.0)
        
        self.evaluation_prompt = ChatPromptTemplate.from_template("""
        You are an expert career coach evaluator. Assess how empathetic and supportive the coaching response is.

        User Query: {input}
        Coach Response: {output}

        Evaluate the empathy and support level on a scale of 0.0 to 1.0 based on:
        1. Acknowledgment of user's feelings and concerns
        2. Encouraging and positive tone
        3. Understanding of user's perspective
        4. Emotional support and validation
        5. Appropriate level of warmth and care

        Provide your score as a float between 0.0 and 1.0, where:
        - 0.0-0.3: Cold, dismissive, or insensitive response
        - 0.4-0.6: Somewhat supportive but lacks warmth
        - 0.7-0.9: Empathetic and encouraging response
        - 1.0: Exceptionally supportive and understanding

        Score: [Your score as a float]
        Reasoning: [Brief explanation of your scoring]
        """)
    
    async def ascore(self, input: str, output: str, **kwargs) -> float:
        """Asynchronously score the empathy of coaching response."""
        try:
            chain = self.evaluation_prompt | self.model
            result = await chain.ainvoke({
                "input": input,
                "output": output
            })
            
            # Extract score from response
            response_text = result.content
            score_line = [line for line in response_text.split('\n') if line.startswith('Score:')]
            
            if score_line:
                score_str = score_line[0].replace('Score:', '').strip()
                score = float(score_str)
                return max(0.0, min(1.0, score))
            
            return 0.5
            
        except Exception as e:
            logger.error(f"Error in EmpathyMetric: {e}")
            return 0.5

    def score(self, input: str, output: str, **kwargs) -> float:
        """Synchronously score the empathy of coaching response."""
        return asyncio.run(self.ascore(input, output, **kwargs))


class GoalAlignmentMetric(BaseMetric):
    """Evaluates how well the coaching advice aligns with stated career goals."""
    
    name = "goal_alignment"
    
    def __init__(self, model_name: str = "gpt-4o-mini"):
        self.model = ChatOpenAI(model=model_name, temperature=0.0)
        
        self.evaluation_prompt = ChatPromptTemplate.from_template("""
        You are an expert career coach evaluator. Assess how well the coaching advice aligns with the user's career goals.

        User Query: {input}
        Coach Response: {output}
        Context (if available): {context}

        Evaluate the goal alignment on a scale of 0.0 to 1.0 based on:
        1. Direct relevance to stated career objectives
        2. Consistency with user's career stage and aspirations
        3. Appropriate prioritization of goals
        4. Recognition of user's constraints and preferences
        5. Strategic alignment with long-term career vision

        Provide your score as a float between 0.0 and 1.0, where:
        - 0.0-0.3: Advice contradicts or ignores stated goals
        - 0.4-0.6: Somewhat relevant but not well-aligned
        - 0.7-0.9: Well-aligned with user's career objectives
        - 1.0: Perfectly aligned and strategically sound

        Score: [Your score as a float]
        Reasoning: [Brief explanation of your scoring]
        """)
    
    async def ascore(self, input: str, output: str, context: str = "", **kwargs) -> float:
        """Asynchronously score the goal alignment of coaching advice."""
        try:
            chain = self.evaluation_prompt | self.model
            result = await chain.ainvoke({
                "input": input,
                "output": output,
                "context": context
            })
            
            # Extract score from response
            response_text = result.content
            score_line = [line for line in response_text.split('\n') if line.startswith('Score:')]
            
            if score_line:
                score_str = score_line[0].replace('Score:', '').strip()
                score = float(score_str)
                return max(0.0, min(1.0, score))
            
            return 0.5
            
        except Exception as e:
            logger.error(f"Error in GoalAlignmentMetric: {e}")
            return 0.5

    def score(self, input: str, output: str, context: str = "", **kwargs) -> float:
        """Synchronously score the goal alignment of coaching advice."""
        return asyncio.run(self.ascore(input, output, context, **kwargs))


class CoachingQualityMetric(BaseMetric):
    """Evaluates overall coaching quality including expertise demonstration and methodology."""
    
    name = "coaching_quality"
    
    def __init__(self, model_name: str = "gpt-4o-mini"):
        self.model = ChatOpenAI(model=model_name, temperature=0.0)
        
        self.evaluation_prompt = ChatPromptTemplate.from_template("""
        You are an expert career coach evaluator. Assess the overall quality of the coaching response.

        User Query: {input}
        Coach Response: {output}

        Evaluate the coaching quality on a scale of 0.0 to 1.0 based on:
        1. Demonstration of career coaching expertise
        2. Use of appropriate coaching methodologies
        3. Quality of questions asked to the user
        4. Professional communication style
        5. Evidence-based recommendations
        6. Appropriate use of career development frameworks

        Provide your score as a float between 0.0 and 1.0, where:
        - 0.0-0.3: Poor coaching quality, lacks expertise
        - 0.4-0.6: Average coaching with some good elements
        - 0.7-0.9: High-quality coaching with clear expertise
        - 1.0: Exceptional coaching quality and methodology

        Score: [Your score as a float]
        Reasoning: [Brief explanation of your scoring]
        """)
    
    async def ascore(self, input: str, output: str, **kwargs) -> float:
        """Asynchronously score the coaching quality."""
        try:
            chain = self.evaluation_prompt | self.model
            result = await chain.ainvoke({
                "input": input,
                "output": output
            })
            
            # Extract score from response
            response_text = result.content
            score_line = [line for line in response_text.split('\n') if line.startswith('Score:')]
            
            if score_line:
                score_str = score_line[0].replace('Score:', '').strip()
                score = float(score_str)
                return max(0.0, min(1.0, score))
            
            return 0.5
            
        except Exception as e:
            logger.error(f"Error in CoachingQualityMetric: {e}")
            return 0.5

    def score(self, input: str, output: str, **kwargs) -> float:
        """Synchronously score the coaching quality."""
        return asyncio.run(self.ascore(input, output, **kwargs))


# Convenience function to get all custom metrics
def get_career_coaching_metrics() -> List[BaseMetric]:
    """Get all custom career coaching evaluation metrics."""
    return [
        ActionabilityMetric(),
        EmpathyMetric(),
        GoalAlignmentMetric(),
        CoachingQualityMetric()
    ]
