"use client"

import { useRouter } from "next/navigation"

import { Card, CardContent } from "@/components/ui/card"

interface Module {
  id: string
  title: string
  description: string
  duration: string
  imageSrc: string
  color: string
  href: string
}

interface ModuleCardProps {
  module: Module
}

export const ModuleCard = ({ module }: ModuleCardProps) => {
  const router = useRouter()
  return (
    <Card
      className="h-full cursor-pointer transition-shadow hover:shadow-lg"
      onClick={() => router.push(module.href)}
    >
      <div
        className={`h-48 rounded-t-lg ${module.color} flex items-center justify-center`}
      >
        <img
          src={module.imageSrc}
          alt={module.title}
          className="h-full w-full rounded-t-lg object-cover"
        />
      </div>
      <CardContent className="p-6">
        <div className="mb-3">
          <span className="rounded bg-muted px-2 py-1 text-sm text-muted-foreground">
            Module
          </span>
          <span className="ml-2 rounded bg-muted px-2 py-1 text-sm text-muted-foreground">
            {module.duration}
          </span>
        </div>
        <h3 className="mb-3 text-lg font-semibold text-foreground">
          {module.title}
        </h3>
        <p className="text-sm leading-relaxed text-muted-foreground">
          {module.description}
        </p>
      </CardContent>
    </Card>
  )
}
