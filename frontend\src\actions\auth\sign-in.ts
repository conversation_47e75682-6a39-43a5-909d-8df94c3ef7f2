"use server"

import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"

/**
 * Sign in with OAuth provider
 * @param provider - The OAuth provider to sign in with
 */
export async function signIn(provider: "google"): Promise<"error" | "success"> {
  try {
    const supabase = await createClient()

    // Get the redirect URL from environment variable
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const redirectTo = `${baseUrl}/auth/callback`

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    })
    
    if (error) {
      console.error("OAuth sign-in error:", error)
      return "error"
    }
    
    if (data?.url) {
      revalidatePath("/", "layout")
      redirect(data.url)
    }

    return "success"
  } catch (err) {
    console.error(err)
    return "error"
  }
}
