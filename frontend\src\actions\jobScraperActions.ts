import api from '@/services/apiService';
import { getApiUrl, API_ROUTES } from '@/config/apiEndpoints';

export interface JobScraperResponse {
  job_description: string;
  company_description: string;
  company_name: string;
}

export interface JobScraperError {
  message: string;
  code?: string;
  status?: number;
}

/**
 * Scrapes job and company descriptions from a job posting URL
 * 
 * @param url - The URL of the job posting to scrape
 * @returns Promise with job and company descriptions
 */
export const scrapeJobUrl = async (url: string): Promise<JobScraperResponse> => {
  try {
    // Log the URL being scraped
    console.log('💬 Sending request to job scraper API:', API_ROUTES.SCRAPE_JOB_URL);
    console.log('URL to scrape:', url);
    
    // Make sure we're sending the exact format expected by the backend
    // The backend expects: { "url": "https://example.com" }
    const payload = {
      url: url
    };
    
    console.log('Request payload:', payload);
    
    // Use direct axios config to ensure proper content-type
    const response = await api.post(API_ROUTES.SCRAPE_JOB_URL, payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('📥 Raw API response status:', response.status);
    console.log('📚 Response data:', response.data);
    
    // Make sure to verify the structure of the response data
    if (!response.data.job_description) {
      console.warn('⚠️ Job description is missing in the API response!');
    }
    
    return response.data;
  } catch (error) {
    console.error('Error scraping job URL:', error);
    
    // Handle specific error cases
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      const status = error.response.status;
      
      if (status === 404) {
        throw { 
          message: 'No data could be extracted from this URL. Please try another URL or enter the job details manually.',
          code: 'NOT_FOUND',
          status: 404
        };
      } else if (status === 429) {
        throw { 
          message: 'Too many requests. Please try again later.',
          code: 'RATE_LIMIT',
          status: 429
        };
      } else if (status >= 500) {
        throw { 
          message: 'Server error while scraping the job posting. Please try again later or enter details manually.',
          code: 'SERVER_ERROR',
          status: status
        };
      }
    }
    
    // Generic error case
    throw { 
      message: 'Failed to scrape job posting. Please check the URL or enter details manually.',
      code: 'SCRAPE_ERROR'
    };
  }
};
