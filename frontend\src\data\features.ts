import { type Feature } from "@/types"

export const features: Feature[] = [
  {
    title: "Resume Evaluation",
    description:
      "Get instant insights and feedback on your resume to ensure it meets industry standards and maximizes your chances of landing interviews.",
    image: "/images/features/sh1.png",
  },
  {
    title: "LinkedIn Master",
    description:
      "Optimize your LinkedIn profile with AI-driven suggestions to enhance visibility and attract recruiters.",
    image: "/images/features/sh1.png",
  },
  {
    title: "Interview Prep",
    description:
      "Prepare for interviews with curated questions, detailed answer guidelines, and performance analysis tools.",
    image: "/images/features/sh1.png",
  },
  {
    title: "AI Cover Letter",
    description:
      "Generate tailored, professional cover letters that align with job descriptions and highlight your strengths.",
    image: "/images/features/sh1.png",
  },
  {
    title: "Tailor Resume",
    description:
      "Customize your resume for specific roles with AI recommendations and keyword optimization.",
    image: "/images/features/sh1.png",
  },
]
