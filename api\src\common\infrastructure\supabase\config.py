from pydantic_settings import BaseSettings
from pydantic import Field


class SupabaseSettings(BaseSettings):
    """Supabase connection settings."""

    SUPABASE_URL: str = Field(..., description="Supabase project URL")
    SUPABASE_KEY: str = Field(..., description="Supabase service role key")
    
    model_config = {
        "env_prefix": "",
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "extra": "ignore"  # Allow extra fields in environment variables
    }


supabase_settings = SupabaseSettings()
