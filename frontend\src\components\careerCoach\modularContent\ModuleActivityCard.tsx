// components/modules/ModuleActivityCard.tsx
interface ModuleActivityCardProps {
  emoji: string
  title: string
  duration: string
  description: string
}

export function ModuleActivityCard({
  emoji,
  title,
  duration,
  description,
}: ModuleActivityCardProps) {
  return (
    <div className="flex flex-col gap-2 rounded-xl bg-gray-50 p-4 shadow-sm">
      <div className="flex items-center justify-between">
        <span className="text-xl">{emoji}</span>
        <span className="text-sm text-gray-500">{duration}</span>
      </div>
      <h3 className="text-md font-semibold text-gray-900">{title}</h3>
      <p className="text-sm text-gray-700">{description}</p>
    </div>
  )
}
