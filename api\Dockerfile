FROM python:3.11-slim

# Set the working directory.
WORKDIR /app

RUN apt-get update && apt-get install -y \
    texlive-latex-base \
    texlive-fonts-recommended \
    texlive-fonts-extra \
    && rm -rf /var/lib/apt/lists/*

# Copy the application files
COPY pyproject.toml README.md requirements.txt .env ./
COPY src/ src/
COPY tools/ tools/

# Install uv for faster package installation
RUN pip install uv

# Install dependencies without installing the package itself
RUN uv pip install --system -e .

# Pre-download the embedding model to reduce startup time
RUN python -c "from sentence_transformers import SentenceTransformer; SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')"

# Set the Python path to include src directory directly
ENV PYTHONPATH="/app:/app/src"

# Set environment variables for faster startup
ENV API_PORT=8000
ENV RAG_DEVICE=cpu
ENV OPIK_API_KEY=""

# Expose the port the app runs on
EXPOSE 8000

# Copy startup script
COPY start_server.py /app/start_server.py

# Use Python to start the server with proper path setup
CMD ["python", "/app/start_server.py"]
