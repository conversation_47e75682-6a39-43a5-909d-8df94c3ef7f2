"use client"

import * as React from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { signInWithPassword } from "@/actions/auth/sign-in-with-password"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"

import { DEFAULT_SIGNIN_REDIRECT } from "@/config/defaults"
import {
  signInWithPasswordSchema,
  type SignInWithPasswordFormInput,
} from "@/validations/auth"

import { useToast } from "@/hooks/use-toast"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Icons } from "@/components/icons"
import { PasswordInput } from "@/components/password-input"

export function SignInWithPasswordForm(): JSX.Element {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const [isPending, startTransition] = React.useTransition()
  
  // Check URL for any error param
  React.useEffect(() => {
    const errorMessage = searchParams.get("error")
    if (errorMessage) {
      toast({
        title: "Authentication Error",
        description: decodeURIComponent(errorMessage),
        variant: "destructive",
      })
    }
  }, [searchParams, toast])

  const form = useForm<SignInWithPasswordFormInput>({
    resolver: zodResolver(signInWithPasswordSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  function onSubmit(formData: SignInWithPasswordFormInput) {
    startTransition(async () => {
      try {
        const message = await signInWithPassword({
          email: formData.email,
          password: formData.password,
        })

        switch (message) {
          case "invalid-input":
            toast({
              title: "Email already in use with another provider",
              description: "Try signing in with Google instead",
              variant: "destructive",
            })
            break
          case "unverified-email":
            toast({
              title: "Email not verified",
              description: "Please check your inbox and verify your email before signing in",
              variant: "destructive",
            })
            break
          case "invalid-credentials":
            toast({
              title: "Invalid credentials",
              description: "Please check your email and password",
              variant: "destructive",
            })
            form.setError("password", { 
              message: "Incorrect email or password" 
            })
            break
          case "success":
            toast({
              title: "Welcome back!",
              description: "Successfully signed in",
              variant: "default",
            })
            router.push("/app")
            break
          case "onboarding":
            toast({
              title: "Welcome!",
              description: "Please complete your profile to continue",
              variant: "default",
            })
            router.push("/onboarding")
            break
          default:
            toast({
              title: "Authentication failed",
              description: "Please try again or use another sign in method",
              variant: "destructive",
            })
        }
      } catch (error) {
        console.error(error)
        toast({
          title: "Something went wrong",
          description: "Our servers are having issues. Please try again later.",
          variant: "destructive",
        })
      }
    })
  }

  return (
    <Form {...form}>
      <form
        className="grid w-full gap-4"
        onSubmit={(...args) => void form.handleSubmit(onSubmit)(...args)}
      >
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  autoComplete="email"
                  placeholder="<EMAIL>"
                  className="bg-background"
                  {...field}
                />
              </FormControl>
              <FormMessage className="pt-1 text-xs" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <PasswordInput 
                  placeholder="••••••••" 
                  autoComplete="current-password"
                  className="bg-background"
                  {...field} 
                />
              </FormControl>
              <FormMessage className="pt-1 text-xs" />
            </FormItem>
          )}
        />
        <Button 
          type="submit"
          disabled={isPending} 
          className="mt-2 w-full"
        >
          {isPending ? (
            <>
              <Icons.spinner
                className="mr-2 size-4 animate-spin"
                aria-hidden="true"
              />
              <span>Signing in...</span>
            </>
          ) : (
            <span>Sign in</span>
          )}
          <span className="sr-only">Sign in with email and password</span>
        </Button>
      </form>
    </Form>
  )
}
