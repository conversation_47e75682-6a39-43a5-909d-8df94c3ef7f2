#!/bin/bash

# Script to deploy frontend to AWS Amplify
# Usage: ./deploy_frontend.sh [aws-region] [amplify-app-id] [branch-name] [frontend-dir]

set -e

AWS_REGION=$1
AMPLIFY_APP_ID=$2
BRANCH_NAME=${3:-main}
FRONTEND_DIR=$4

echo "Deploying frontend to AWS Amplify..."

# Check if the app exists
APP_EXISTS=$(aws amplify get-app --app-id ${AMPLIFY_APP_ID} --region ${AWS_REGION} --query "app.name" --output text 2>/dev/null || echo "")

if [ -z "$APP_EXISTS" ]; then
  echo "Error: Amplify app ID ${AMPLIFY_APP_ID} not found."
  echo "Please create an Amplify app first using the AWS Console or CLI."
  exit 1
fi

# Check if the branch exists
BRANCH_EXISTS=$(aws amplify get-branch --app-id ${AMPLIFY_APP_ID} --branch-name ${<PERSON>ANCH_NAME} --region ${AWS_REGION} --query "branch.branchName" --output text 2>/dev/null || echo "")

if [ -z "$BRANCH_EXISTS" ]; then
  echo "Branch ${BRANCH_NAME} does not exist. Creating it..."
  aws amplify create-branch \
    --app-id ${AMPLIFY_APP_ID} \
    --branch-name ${BRANCH_NAME} \
    --region ${AWS_REGION}
  
  echo "Branch created. Setting up webhook for automatic deployments..."
  # This will enable auto-builds when code is pushed to this branch
  aws amplify update-branch \
    --app-id ${AMPLIFY_APP_ID} \
    --branch-name ${BRANCH_NAME} \
    --enable-auto-build \
    --region ${AWS_REGION}
fi

# Build the frontend
echo "Building frontend application..."
cd ${FRONTEND_DIR}

# Check for package manager and build the app
if [ -f "package.json" ]; then
  if [ -f "yarn.lock" ]; then
    echo "Using Yarn to build..."
    yarn install
    yarn build
  else
    echo "Using NPM to build..."
    npm install
    npm run build
  fi
else
  echo "Error: No package.json found in ${FRONTEND_DIR}"
  exit 1
fi

# Create a zip file of the build directory
BUILD_DIR="build"
if [ -d "dist" ]; then
  BUILD_DIR="dist"
fi

echo "Packaging ${BUILD_DIR} for deployment..."
ZIP_FILE="../amplify-build.zip"
(cd ${BUILD_DIR} && zip -r ${ZIP_FILE} .)

# Deploy to Amplify using a manual job
echo "Starting manual deployment job..."
JOB_ID=$(aws amplify start-job \
  --app-id ${AMPLIFY_APP_ID} \
  --branch-name ${BRANCH_NAME} \
  --job-type MANUAL_DEPLOY \
  --source-url file://${ZIP_FILE} \
  --region ${AWS_REGION} \
  --query "jobSummary.jobId" \
  --output text)

echo "Deployment job started with ID: ${JOB_ID}"
echo "Checking deployment status..."

# Wait for job to complete
while true; do
  STATUS=$(aws amplify get-job \
    --app-id ${AMPLIFY_APP_ID} \
    --branch-name ${BRANCH_NAME} \
    --job-id ${JOB_ID} \
    --region ${AWS_REGION} \
    --query "job.summary.status" \
    --output text)
  
  echo "Job status: ${STATUS}"
  
  if [ "$STATUS" == "SUCCEED" ]; then
    echo "Deployment completed successfully!"
    break
  elif [ "$STATUS" == "FAILED" ]; then
    echo "Deployment failed. Check the Amplify console for details."
    exit 1
  elif [ "$STATUS" == "CANCELLED" ]; then
    echo "Deployment was cancelled."
    exit 1
  fi
  
  echo "Waiting for deployment to complete..."
  sleep 30
done

# Get the deployment URL
APP_URL=$(aws amplify get-app --app-id ${AMPLIFY_APP_ID} --region ${AWS_REGION} \
  --query "app.defaultDomain" --output text)

echo "Frontend deployed successfully!"
echo "Your application is available at: https://${BRANCH_NAME}.${APP_URL}"

# Clean up
rm -f ../amplify-build.zip
