"""
Enhanced monitoring and logging system for career coaches.
"""

import time
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from functools import wraps
import asyncio

from loguru import logger
import opik
from career_coaches.config import settings
from career_coaches.application.long_term_memory import CareerCoachLongTermMemoryCreator


@dataclass
class ConversationMetrics:
    """Metrics for a single conversation."""
    user_id: str
    coach_id: str
    session_id: str
    start_time: str
    end_time: str
    duration_seconds: float
    message_count: int
    user_message_count: int
    coach_message_count: int
    response_times: List[float]
    average_response_time: float
    user_satisfaction: Optional[float] = None
    coaching_effectiveness: Optional[float] = None
    memory_retrievals: int = 0
    memory_updates: int = 0
    web_tool_usage: int = 0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []


@dataclass
class CoachPerformanceMetrics:
    """Performance metrics for a specific coach."""
    coach_id: str
    period_start: str
    period_end: str
    total_conversations: int
    total_users: int
    average_conversation_duration: float
    average_response_time: float
    average_user_satisfaction: float
    average_coaching_effectiveness: float
    total_goals_set: int
    total_achievements_recorded: int
    memory_operations: int
    error_rate: float
    top_user_concerns: List[str]
    improvement_suggestions: List[str]


class CareerCoachMonitor:
    """Comprehensive monitoring system for career coaches."""
    
    def __init__(self):
        self.memory_creator = CareerCoachLongTermMemoryCreator.build_from_settings()
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
    def start_conversation_tracking(self, user_id: str, coach_id: str, session_id: str) -> None:
        """Start tracking a new conversation."""
        self.active_sessions[session_id] = {
            "user_id": user_id,
            "coach_id": coach_id,
            "start_time": datetime.utcnow().isoformat(),
            "message_count": 0,
            "user_message_count": 0,
            "coach_message_count": 0,
            "response_times": [],
            "memory_retrievals": 0,
            "memory_updates": 0,
            "web_tool_usage": 0,
            "errors": [],
            "last_message_time": time.time()
        }
        
        logger.info(f"Started tracking conversation {session_id} for user {user_id} with coach {coach_id}")
    
    def log_message(self, session_id: str, message_type: str, response_time: float = None) -> None:
        """Log a message in the conversation."""
        if session_id not in self.active_sessions:
            logger.warning(f"Session {session_id} not found for message logging")
            return
        
        session = self.active_sessions[session_id]
        session["message_count"] += 1
        
        if message_type == "user":
            session["user_message_count"] += 1
            session["last_message_time"] = time.time()
        elif message_type == "coach":
            session["coach_message_count"] += 1
            if response_time:
                session["response_times"].append(response_time)
    
    def log_memory_operation(self, session_id: str, operation_type: str) -> None:
        """Log memory operations (retrieval or update)."""
        if session_id not in self.active_sessions:
            return
        
        if operation_type == "retrieval":
            self.active_sessions[session_id]["memory_retrievals"] += 1
        elif operation_type == "update":
            self.active_sessions[session_id]["memory_updates"] += 1
    
    def log_web_tool_usage(self, session_id: str) -> None:
        """Log web tool usage."""
        if session_id not in self.active_sessions:
            return
        
        self.active_sessions[session_id]["web_tool_usage"] += 1
    
    def log_error(self, session_id: str, error: str) -> None:
        """Log an error in the conversation."""
        if session_id not in self.active_sessions:
            return
        
        self.active_sessions[session_id]["errors"].append({
            "error": error,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        logger.error(f"Error in session {session_id}: {error}")
    
    def end_conversation_tracking(
        self, 
        session_id: str, 
        user_satisfaction: float = None,
        coaching_effectiveness: float = None
    ) -> ConversationMetrics:
        """End conversation tracking and return metrics."""
        if session_id not in self.active_sessions:
            logger.warning(f"Session {session_id} not found for ending tracking")
            return None
        
        session = self.active_sessions[session_id]
        end_time = datetime.utcnow().isoformat()
        start_time = session["start_time"]
        
        # Calculate duration
        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        duration = (end_dt - start_dt).total_seconds()
        
        # Calculate average response time
        response_times = session["response_times"]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0.0
        
        metrics = ConversationMetrics(
            user_id=session["user_id"],
            coach_id=session["coach_id"],
            session_id=session_id,
            start_time=start_time,
            end_time=end_time,
            duration_seconds=duration,
            message_count=session["message_count"],
            user_message_count=session["user_message_count"],
            coach_message_count=session["coach_message_count"],
            response_times=response_times,
            average_response_time=avg_response_time,
            user_satisfaction=user_satisfaction,
            coaching_effectiveness=coaching_effectiveness,
            memory_retrievals=session["memory_retrievals"],
            memory_updates=session["memory_updates"],
            web_tool_usage=session["web_tool_usage"],
            errors=session["errors"]
        )
        
        # Store metrics in long-term memory
        self._store_conversation_metrics(metrics)
        
        # Clean up active session
        del self.active_sessions[session_id]
        
        logger.info(f"Ended tracking for session {session_id}. Duration: {duration:.2f}s, Messages: {metrics.message_count}")
        return metrics
    
    def _store_conversation_metrics(self, metrics: ConversationMetrics) -> None:
        """Store conversation metrics in long-term memory."""
        try:
            metrics_content = f"""
            Conversation Metrics:
            Session ID: {metrics.session_id}
            User: {metrics.user_id}
            Coach: {metrics.coach_id}
            Duration: {metrics.duration_seconds:.2f} seconds
            Messages: {metrics.message_count} (User: {metrics.user_message_count}, Coach: {metrics.coach_message_count})
            Average Response Time: {metrics.average_response_time:.2f} seconds
            Memory Operations: {metrics.memory_retrievals} retrievals, {metrics.memory_updates} updates
            Web Tool Usage: {metrics.web_tool_usage}
            User Satisfaction: {metrics.user_satisfaction or 'Not rated'}
            Coaching Effectiveness: {metrics.coaching_effectiveness or 'Not rated'}
            Errors: {len(metrics.errors)}
            """
            
            self.memory_creator.add_user_memory(
                user_id=metrics.user_id,
                coach_id=metrics.coach_id,
                content=metrics_content,
                memory_type="metrics",
                importance=0.6,
                metadata={
                    "session_id": metrics.session_id,
                    "metrics_data": asdict(metrics)
                }
            )
        except Exception as e:
            logger.error(f"Failed to store conversation metrics: {e}")
    
    def get_coach_performance_metrics(
        self, 
        coach_id: str, 
        days: int = 7
    ) -> CoachPerformanceMetrics:
        """Get performance metrics for a specific coach over a time period."""
        try:
            from career_coaches.application.long_term_memory import CareerCoachLongTermMemoryRetriever
            
            retriever = CareerCoachLongTermMemoryRetriever.build_from_settings()
            
            # Get all metrics for this coach
            metrics_docs = retriever.retrieve(
                query="conversation metrics",
                coach_id=coach_id,
                memory_type="metrics"
            )
            
            # Filter by time period
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            filtered_metrics = []
            for doc in metrics_docs:
                metrics_data = doc.metadata.get("metrics_data", {})
                if metrics_data:
                    start_time = metrics_data.get("start_time", "")
                    if start_time:
                        try:
                            msg_date = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                            if start_date <= msg_date <= end_date:
                                filtered_metrics.append(metrics_data)
                        except:
                            continue
            
            if not filtered_metrics:
                return CoachPerformanceMetrics(
                    coach_id=coach_id,
                    period_start=start_date.isoformat(),
                    period_end=end_date.isoformat(),
                    total_conversations=0,
                    total_users=0,
                    average_conversation_duration=0.0,
                    average_response_time=0.0,
                    average_user_satisfaction=0.0,
                    average_coaching_effectiveness=0.0,
                    total_goals_set=0,
                    total_achievements_recorded=0,
                    memory_operations=0,
                    error_rate=0.0,
                    top_user_concerns=[],
                    improvement_suggestions=[]
                )
            
            # Calculate aggregated metrics
            total_conversations = len(filtered_metrics)
            unique_users = len(set(m.get("user_id", "") for m in filtered_metrics))
            
            avg_duration = sum(m.get("duration_seconds", 0) for m in filtered_metrics) / total_conversations
            avg_response_time = sum(m.get("average_response_time", 0) for m in filtered_metrics) / total_conversations
            
            satisfactions = [m.get("user_satisfaction") for m in filtered_metrics if m.get("user_satisfaction")]
            avg_satisfaction = sum(satisfactions) / len(satisfactions) if satisfactions else 0.0
            
            effectiveness_scores = [m.get("coaching_effectiveness") for m in filtered_metrics if m.get("coaching_effectiveness")]
            avg_effectiveness = sum(effectiveness_scores) / len(effectiveness_scores) if effectiveness_scores else 0.0
            
            total_memory_ops = sum(
                m.get("memory_retrievals", 0) + m.get("memory_updates", 0) 
                for m in filtered_metrics
            )
            
            total_errors = sum(len(m.get("errors", [])) for m in filtered_metrics)
            error_rate = total_errors / total_conversations if total_conversations > 0 else 0.0
            
            return CoachPerformanceMetrics(
                coach_id=coach_id,
                period_start=start_date.isoformat(),
                period_end=end_date.isoformat(),
                total_conversations=total_conversations,
                total_users=unique_users,
                average_conversation_duration=avg_duration,
                average_response_time=avg_response_time,
                average_user_satisfaction=avg_satisfaction,
                average_coaching_effectiveness=avg_effectiveness,
                total_goals_set=0,  # Would need to query goal data
                total_achievements_recorded=0,  # Would need to query achievement data
                memory_operations=total_memory_ops,
                error_rate=error_rate,
                top_user_concerns=[],  # Would need NLP analysis
                improvement_suggestions=self._generate_improvement_suggestions(avg_satisfaction, avg_effectiveness, error_rate)
            )
            
        except Exception as e:
            logger.error(f"Error calculating coach performance metrics: {e}")
            return None
    
    def _generate_improvement_suggestions(
        self, 
        satisfaction: float, 
        effectiveness: float, 
        error_rate: float
    ) -> List[str]:
        """Generate improvement suggestions based on metrics."""
        suggestions = []
        
        if satisfaction < 3.5:
            suggestions.append("Focus on improving user satisfaction through more empathetic responses")
        
        if effectiveness < 3.5:
            suggestions.append("Enhance coaching effectiveness by providing more actionable advice")
        
        if error_rate > 0.1:
            suggestions.append("Reduce error rate by improving system reliability and error handling")
        
        if not suggestions:
            suggestions.append("Maintain current high performance standards")
        
        return suggestions


# Global monitor instance
monitor = CareerCoachMonitor()


def track_conversation(func):
    """Decorator to automatically track conversation metrics."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # Extract session info from kwargs
        user_id = kwargs.get('user_id', 'unknown')
        coach_id = kwargs.get('coach_id', 'unknown')
        session_id = f"{user_id}_{coach_id}_{int(time.time())}"
        
        # Start tracking
        monitor.start_conversation_tracking(user_id, coach_id, session_id)
        
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            response_time = time.time() - start_time
            monitor.log_message(session_id, "coach", response_time)
            return result
        except Exception as e:
            monitor.log_error(session_id, str(e))
            raise
        finally:
            monitor.end_conversation_tracking(session_id)
    
    return wrapper
