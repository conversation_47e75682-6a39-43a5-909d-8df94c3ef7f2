"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { getCoaches, type Coach } from "@/services/careerCoachService"
import {
  Briefcase,
  FileText,
  Linkedin,
  MessageCircle,
  ArrowRight,
  Sparkles
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

// Coach icon mapping
const coachIcons = {
  "career_assessment": Briefcase,
  "resume_builder": FileText,
  "linkedin_optimizer": <PERSON>ed<PERSON>,
  "interview_coach": MessageCircle,
}

const coachColors = {
  "career_assessment": "bg-primary",
  "resume_builder": "bg-secondary",
  "linkedin_optimizer": "bg-green",
  "interview_coach": "bg-green-dark",
}

export default function CareerCoachPage() {
  const [coaches, setCoaches] = useState<Coach[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    async function loadCoaches() {
      try {
        setLoading(true)
        console.log("Attempting to fetch coaches from API...")
        const coachesData = await getCoaches()
        console.log("Coaches data received:", coachesData)
        // Limit to first 4 coaches
        setCoaches(coachesData.slice(0, 4))
        setError(null)
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unknown error"
        setError(`Failed to load coaches: ${errorMessage}`)
        console.error("Error loading coaches:", err)
      } finally {
        setLoading(false)
      }
    }
    loadCoaches()
  }, [])

  const getCoachIcon = (coachId: string) => {
    return coachIcons[coachId as keyof typeof coachIcons] || Briefcase
  }

  const getCoachColor = (coachId: string) => {
    return coachColors[coachId as keyof typeof coachColors] || "bg-blue-500"
  }

  const handleCoachSelect = (coach: Coach) => {
    router.push(`/app/careerCoach/${coach.id}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-primary/10 flex items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 size-16 animate-spin rounded-full border-4 border-primary/20 border-t-primary"></div>
          <p className="text-xl text-gray-600">Finding your perfect coach...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-primary/10 flex items-center justify-center p-6">
        <div className="rounded-2xl border border-red-100 bg-white p-8 text-center shadow-lg">
          <p className="mb-4 text-lg text-red-600">{error}</p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-red-600 hover:bg-red-700"
          >
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-primary/10">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-white">
        <div className="mx-auto max-w-4xl px-6 py-16 text-center">
          <div className="mb-6 flex justify-center">
            <div className="rounded-full bg-primary/10 p-4">
              <Sparkles className="size-8 text-primary" />
            </div>
          </div>
          <h1 className="mb-4 text-4xl font-bold text-gray-900 md:text-5xl">
            Career Coach
          </h1>
          <p className="mx-auto max-w-2xl text-lg text-gray-600">
            Get personalized guidance from expert career coaches who&apos;ve helped thousands land their dream jobs
          </p>
        </div>
      </div>

      {/* Coaches Grid */}
      <div className="mx-auto max-w-6xl px-6 py-16">
        <div className="mb-12 text-center">
          <h2 className="mb-4 text-3xl font-bold text-gray-900">
            Choose Your Coach
          </h2>
          <p className="text-gray-600">
            Select the coach that best matches your career goals
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {coaches.map((coach, index) => {
            const IconComponent = getCoachIcon(coach.id)
            const colorClass = getCoachColor(coach.id)

            return (
              <Card
                key={coach.id}
                className="group cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl"
                onClick={() => handleCoachSelect(coach)}
              >
                <CardContent className="p-8 text-center">
                  {/* Icon */}
                  <div className={`mx-auto mb-6 flex size-20 items-center justify-center rounded-full ${colorClass} transition-transform duration-300 group-hover:scale-110`}>
                    <IconComponent className="size-10 text-white" />
                  </div>

                  {/* Coach Info */}
                  <h3 className="mb-3 text-xl font-bold text-gray-900">
                    {coach.name}
                  </h3>
                  <p className="mb-4 text-sm text-gray-600">
                    {coach.specialty}
                  </p>

                  {/* Focus Areas */}
                  <div className="mb-6 space-y-1">
                    {coach.focus_areas.slice(0, 3).map((area, idx) => (
                      <p key={idx} className="text-xs text-gray-500">
                        • {area}
                      </p>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <Button
                    className="w-full bg-gray-900 text-white transition-colors hover:bg-gray-800 group-hover:bg-primary"
                  >
                    Start Coaching
                    <ArrowRight className="ml-2 size-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </div>
  )
}
