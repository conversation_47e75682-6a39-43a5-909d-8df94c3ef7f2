#!/bin/bash

# Script to deploy backend to AWS App Runner
# Usage: ./deploy_backend.sh [aws-region] [ecr-repo-name] [image-tag] [app-runner-service-name]

set -e

AWS_REGION=$1
ECR_REPO_NAME=$2
IMAGE_TAG=$3
APP_RUNNER_SERVICE_NAME=$4

echo "Deploying backend to App Runner..."

# Get AWS account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
ECR_IMAGE="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO_NAME}:${IMAGE_TAG}"

# Check if App Runner service exists
SERVICE_EXISTS=$(aws apprunner list-services --region ${AWS_REGION} --query "ServiceSummaryList[?ServiceName=='${APP_RUNNER_SERVICE_NAME}'].ServiceArn" --output text || echo "")

if [ -z "$SERVICE_EXISTS" ]; then
  echo "Creating new App Runner service..."
  
  # Create App Runner service role if it doesn't exist
  ROLE_NAME="AppRunnerECRAccessRole"
  ROLE_EXISTS=$(aws iam list-roles --query "Roles[?RoleName=='${ROLE_NAME}'].RoleName" --output text || echo "")
  
  if [ -z "$ROLE_EXISTS" ]; then
    echo "Creating App Runner service role..."
    aws iam create-role \
      --role-name ${ROLE_NAME} \
      --assume-role-policy-document '{
        "Version": "2012-10-17",
        "Statement": [{
          "Effect": "Allow",
          "Principal": {
            "Service": "build.apprunner.amazonaws.com"
          },
          "Action": "sts:AssumeRole"
        }]
      }'
    
    aws iam attach-role-policy \
      --role-name ${ROLE_NAME} \
      --policy-arn arn:aws:iam::aws:policy/service-role/AWSAppRunnerServicePolicyForECRAccess
    
    # Wait for role propagation
    echo "Waiting for role propagation..."
    sleep 15
  fi
  
  ROLE_ARN=$(aws iam get-role --role-name ${ROLE_NAME} --query 'Role.Arn' --output text)
  
  # Create new App Runner service
  aws apprunner create-service \
    --service-name ${APP_RUNNER_SERVICE_NAME} \
    --source-configuration '{
      "AuthenticationConfiguration": {
        "AccessRoleArn": "'"${ROLE_ARN}"'"
      },
      "AutoDeploymentsEnabled": true,
      "ImageRepository": {
        "ImageIdentifier": "'"${ECR_IMAGE}"'",
        "ImageConfiguration": {
          "Port": "8000",
          "RuntimeEnvironmentVariables": {
            "API_PORT": "8000",
            "PYTHONPATH": "/app:/app/src"
          }
        },
        "ImageRepositoryType": "ECR"
      }
    }' \
    --instance-configuration '{
      "Cpu": "1 vCPU",
      "Memory": "2 GB"
    }' \
    --health-check-configuration '{
      "Protocol": "HTTP",
      "Path": "/health",
      "Interval": 10,
      "Timeout": 5,
      "HealthyThreshold": 3,
      "UnhealthyThreshold": 3
    }' \
    --auto-scaling-configuration-arn $(aws apprunner create-auto-scaling-configuration \
      --auto-scaling-configuration-name ${APP_RUNNER_SERVICE_NAME}-config \
      --min-size 1 \
      --max-size 10 \
      --query AutoScalingConfiguration.AutoScalingConfigurationArn \
      --output text) \
    --region ${AWS_REGION} \
    --query "Service.ServiceUrl" \
    --output text
else
  echo "Updating existing App Runner service..."
  aws apprunner update-service \
    --service-arn ${SERVICE_EXISTS} \
    --source-configuration '{
      "AutoDeploymentsEnabled": true,
      "ImageRepository": {
        "ImageIdentifier": "'"${ECR_IMAGE}"'",
        "ImageConfiguration": {
          "Port": "8000",
          "RuntimeEnvironmentVariables": {
            "API_PORT": "8000",
            "PYTHONPATH": "/app:/app/src"
          }
        },
        "ImageRepositoryType": "ECR"
      }
    }' \
    --region ${AWS_REGION}
fi

echo "Deployment complete! Your service will be available shortly at:"
aws apprunner list-services --region ${AWS_REGION} --query "ServiceSummaryList[?ServiceName=='${APP_RUNNER_SERVICE_NAME}'].ServiceUrl" --output text
