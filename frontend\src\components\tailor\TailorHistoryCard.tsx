import { useState } from "react"
import { v4 as uuidv4 } from "uuid"
import { Co<PERSON>, Download, Refresh<PERSON><PERSON>, <PERSON> } from "lucide-react"
import { createClient } from "@/lib/supabase/client"

import type { Application, Document, DocumentStatus } from "@/types/application"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"

interface TailorHistoryCardProps {
  record: Application
  onRefresh: () => Promise<void>
}

export function TailorHistoryCard({ record, onRefresh }: TailorHistoryCardProps) {
  const [showDetails, setShowDetails] = useState(false)
  const [showCoverLetter, setShowCoverLetter] = useState(false)
  
  // Find resume and cover letter documents
  const resumeDoc = record.documents?.find(doc => doc.document_type === 'resume')
  const coverLetterDoc = record.documents?.find(doc => doc.document_type === 'cover_letter')

  const getStatusBadge = (status: DocumentStatus) => {
    switch (status) {
      case "completed":
        return (
          <Badge className="border-0 bg-green-100 text-green-700">
            Completed
          </Badge>
        )
      case "processing":
        return (
          <Badge className="border-0 bg-blue-100 text-blue-700">
            Processing
          </Badge>
        )
      case "pending":
        return (
          <Badge className="border-0 bg-gray-100 text-gray-700">
            Pending
          </Badge>
        )
      case "failed":
        return (
          <Badge className="border-0 bg-red-100 text-red-700">Failed</Badge>
        )
      default:
        return (
          <Badge className="border-0 bg-gray-100 text-gray-700">{status}</Badge>
        )
    }
  }
  
  // Calculate progress based on document statuses
  const calculateProgress = () => {
    if (!record.documents || record.documents.length === 0) return 0
    
    const total = record.documents.length
    const completed = record.documents.filter(doc => doc.status === "completed").length
    const processing = record.documents.filter(doc => doc.status === "processing").length
    
    // Each processing document counts as 50% progress
    return Math.round(((completed + (processing * 0.5)) / total) * 100)
  }
  
  // Handle document regeneration
  const handleRegenerateDocument = async (document: Document) => {
    try {
      const supabase = createClient()
      
      // Update document status to pending
      await supabase
        .from("tailored_documents")
        .update({ 
          status: "pending", 
          updated_at: new Date().toISOString()
        })
        .eq("id", document.id)
      
      // Call the edge function to regenerate the document
      const { error: funcError } = await supabase.functions.invoke('process-tailoring-request', {
        body: JSON.stringify({
          applicationId: record.id,  // This is the request_id the edge function expects
          resumeId: record.resume_id,
          jobDescription: record.job.job_description,
          generateResume: document.document_type === 'resume',
          generateCoverLetter: document.document_type === 'cover_letter',
        }),
      })
      
      if (funcError) {
        console.error('Error invoking edge function:', funcError)
        toast({
          title: "Regeneration Failed",
          description: "Failed to initiate document regeneration. Please try again.",
          variant: "destructive"
        })
      } else {
        toast({
          title: "Regeneration Initiated",
          description: `Your ${document.document_type === 'resume' ? 'resume' : 'cover letter'} is being regenerated.`,
        })
      }
      
      // Refresh the data
      await onRefresh()
    } catch (error) {
      console.error("Error regenerating document:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred.",
        variant: "destructive"
      })
    }
  }

  // Handle copy to clipboard
  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        toast({
          title: "Copied!",
          description: "Cover letter text copied to clipboard",
        })
      })
      .catch(() => {
        toast({
          title: "Failed to copy",
          description: "Please try again",
          variant: "destructive"
        })
      })
  }

  return (
    <>
      <Card className="overflow-hidden">
        <CardContent className="p-4">
          <div className="flex flex-col gap-2">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {record.job.job_title}
              </h3>
              <p className="text-sm text-gray-600">{record.company.name}</p>
              <p className="text-xs text-gray-500">
                {new Date(record.applied_date).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mt-4">
              {/* Resume Card */}
              <div className="border rounded-md p-3 bg-gray-50">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-medium text-sm">Resume</h4>
                    {resumeDoc && getStatusBadge(resumeDoc.status)}
                  </div>
                </div>
                
                {resumeDoc?.status === 'processing' && (
                  <div className="mt-2">
                    <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                      <div className="h-full animate-pulse bg-blue-500" style={{ width: '60%' }} />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">Processing...</p>
                  </div>
                )}
                
                {resumeDoc?.status === 'failed' && (
                  <p className="text-xs text-red-600 mt-1">Generation failed</p>
                )}
                
                <div className="flex mt-2 gap-2">
                  {resumeDoc?.status === 'completed' && resumeDoc.file_url && (
                    <Button 
                      size="sm"
                      variant="outline"
                      onClick={() => window.open(resumeDoc.file_url, "_blank")}
                      className="w-full"
                    >
                      <Download className="mr-2 size-4" />
                      Download
                    </Button>
                  )}
                  
                  {resumeDoc && (resumeDoc.status === 'completed' || resumeDoc.status === 'failed') && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleRegenerateDocument(resumeDoc)}
                      className="w-full"
                    >
                      <RefreshCw className="mr-2 size-4" />
                      Regenerate
                    </Button>
                  )}
                </div>
              </div>
              
              {/* Cover Letter Card */}
              <div className="border rounded-md p-3 bg-gray-50">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-medium text-sm">Cover Letter</h4>
                    {coverLetterDoc && getStatusBadge(coverLetterDoc.status)}
                  </div>
                </div>
                
                {coverLetterDoc?.status === 'processing' && (
                  <div className="mt-2">
                    <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                      <div className="h-full animate-pulse bg-blue-500" style={{ width: '60%' }} />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">Processing...</p>
                  </div>
                )}
                
                {coverLetterDoc?.status === 'failed' && (
                  <p className="text-xs text-red-600 mt-1">Generation failed</p>
                )}
                
                <div className="flex mt-2 gap-2">
                  {coverLetterDoc?.status === 'completed' && coverLetterDoc.content && (
                    <Button 
                      size="sm"
                      variant="outline"
                      onClick={() => setShowCoverLetter(true)}
                      className="w-full"
                    >
                      <Eye className="mr-2 size-4" />
                      View
                    </Button>
                  )}
                  
                  {coverLetterDoc && (coverLetterDoc.status === 'completed' || coverLetterDoc.status === 'failed') && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleRegenerateDocument(coverLetterDoc)}
                      className="w-full"
                    >
                      <RefreshCw className="mr-2 size-4" />
                      Regenerate
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="bg-gray-50 p-3">
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-auto text-xs"
            onClick={() => setShowDetails(true)}
          >
            View Details
          </Button>
        </CardFooter>
      </Card>

      {/* Job Details Dialog */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Job Details</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Header Section */}
            <div className="flex items-start justify-between border-b pb-4">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">
                  {record.job.job_title}
                </h3>
                <p className="mt-1 text-gray-600">{record.company.name}</p>
              </div>
            </div>

            {/* Job Description */}
            <div>
              <h4 className="mb-2 font-medium text-gray-900">
                Job Description
              </h4>
              <div className="whitespace-pre-wrap rounded-lg border border-gray-200 bg-gray-50 p-4 text-sm text-gray-700">
                {record.job.job_description}
              </div>
            </div>

            {/* Company Description */}
            {record.company.name && (
              <div>
                <h4 className="mb-2 font-medium text-gray-900">
                  About {record.company.name}
                </h4>
                <div className="whitespace-pre-wrap rounded-lg border border-gray-200 bg-gray-50 p-4 text-sm text-gray-700">
                  {record.company.name}
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Cover Letter Dialog */}
      <Dialog open={showCoverLetter} onOpenChange={setShowCoverLetter}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Cover Letter for {record.job.job_title} at {record.company.name}</DialogTitle>
          </DialogHeader>
          
          <div className="max-h-[60vh] overflow-y-auto">
            <div className="whitespace-pre-wrap rounded-lg border border-gray-200 bg-gray-50 p-4 text-sm">
              {coverLetterDoc?.content}
            </div>
          </div>
          
          <div className="flex justify-end gap-3 mt-4">
            <Button
              variant="outline"
              onClick={() => coverLetterDoc?.content && handleCopyToClipboard(coverLetterDoc.content)}
            >
              <Copy className="mr-2 size-4" />
              Copy to Clipboard
            </Button>
            <DialogClose asChild>
              <Button>Close</Button>
            </DialogClose>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
