import "@copilotkit/react-ui/styles.css"

import React, { ReactNode } from "react"
import { CopilotKit } from "@copilotkit/react-core"

// Use the local API route as the runtime URL
const runtimeUrl = "/api/copilotkit"
// The name of the agent that we'll be using (default to 'sample_agent' if not specified)
const agentName = process.env.NEXT_PUBLIC_COPILOTKIT_AGENT_NAME || "sample_agent"

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <CopilotKit
      runtimeUrl={runtimeUrl}
      agent={agentName}
    >
      {children}
    </CopilotKit>
  )
}
