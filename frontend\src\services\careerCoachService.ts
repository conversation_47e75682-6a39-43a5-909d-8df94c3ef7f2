import { getApiUrl } from "@/config/apiEndpoints"

export interface Coach {
  id: string
  name: string
  specialty: string
  focus_areas: string[]
}

export interface CoachesResponse {
  coaches: Coach[]
}

export interface ChatSession {
  id: string
  user_id: string
  coach_id: string
  chat_session_id: string
  title: string | null
  created_at: string
  updated_at: string
  messages?: any[]
  message_count?: number
}

export interface CreateSessionResponse {
  success: boolean
  session: ChatSession
  chat_session_id: string
  redirect_url: string
}

/**
 * Fetches all available career coaches from the API
 */
export async function getCoaches(): Promise<Coach[]> {
  try {
    const url = getApiUrl("GET_COACHES")
    console.log("Fetching coaches from URL:", url)

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    })

    console.log("Response status:", response.status)
    console.log("Response ok:", response.ok)

    if (!response.ok) {
      throw new Error(`Failed to fetch coaches: ${response.status} ${response.statusText}`)
    }

    const data = (await response.json()) as CoachesResponse
    console.log("Coaches response data:", data)
    return data.coaches
  } catch (error) {
    console.error("Error fetching coaches:", error)
    throw error // Re-throw to let the component handle it
  }
}

/**
 * Fetches a specific coach by ID
 */
export async function getCoachById(id: string): Promise<Coach | null> {
  try {
    const coaches = await getCoaches()
    return coaches.find((coach) => coach.id === id) || null
  } catch (error) {
    console.error(`Error fetching coach with id ${id}:`, error)
    return null
  }
}

/**
 * Creates a new chat session with a coach
 */
export async function createChatSession(
  coachId: string,
  userId: string,
  title?: string
): Promise<CreateSessionResponse | null> {
  try {
    const response = await fetch(`${getApiUrl("GET_COACHES").replace("/coaches", "/create-session")}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        coach_id: coachId,
        user_id: userId,
        title: title,
      }),
    })

    if (!response.ok) {
      throw new Error(`Failed to create session: ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error creating chat session:", error)
    return null
  }
}

/**
 * Fetches chat sessions for a user and coach
 */
export async function getChatSessions(
  userId: string,
  coachId?: string
): Promise<ChatSession[]> {
  try {
    const baseUrl = getApiUrl("GET_COACHES").replace("/coaches", "/supabase-history")
    const url = coachId
      ? `${baseUrl}/coaches/${coachId}/sessions?user_id=${userId}`
      : `${baseUrl}/sessions?user_id=${userId}`

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch sessions: ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching chat sessions:", error)
    return []
  }
}

/**
 * Fetches a specific chat session with messages
 */
export async function getChatSession(
  userId: string,
  sessionId: string
): Promise<ChatSession | null> {
  try {
    const baseUrl = getApiUrl("GET_COACHES").replace("/coaches", "/supabase-history")
    const response = await fetch(`${baseUrl}/sessions/${sessionId}?user_id=${userId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch session: ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching chat session:", error)
    return null
  }
}

/**
 * Sends a chat message
 */
export async function sendChatMessage(
  message: string,
  coachId: string,
  userId: string,
  sessionId: string,
  userContext?: string,
  sessionGoals?: string[],
  webTools?: boolean
): Promise<{ response: string } | null> {
  try {
    const response = await fetch(getApiUrl("GET_COACHES").replace("/coaches", "/chat"), {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        message,
        coach_id: coachId,
        user_id: userId,
        session_id: sessionId,
        user_context: userContext || "",
        session_goals: sessionGoals || [],
        web_tools: webTools || false,
      }),
    })

    if (!response.ok) {
      throw new Error(`Failed to send message: ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error sending chat message:", error)
    return null
  }
}
