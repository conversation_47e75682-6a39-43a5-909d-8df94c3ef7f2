#!/usr/bin/env node

/**
 * OAuth Configuration Test Script
 * 
 * This script helps verify that OAuth configuration is working correctly
 * by testing the environment variables and Supabase connection.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testOAuthConfig() {
  console.log('🔍 Testing OAuth Configuration...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log(`NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
  console.log(`NEXT_PUBLIC_APP_URL: ${process.env.NEXT_PUBLIC_APP_URL || 'undefined'}`);
  console.log(`NEXT_PUBLIC_SUPABASE_URL: ${process.env.NEXT_PUBLIC_SUPABASE_URL || 'undefined'}`);
  console.log(`NEXT_PUBLIC_SUPABASE_ANON_KEY: ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing'}\n`);

  // Check required variables
  const requiredVars = [
    'NEXT_PUBLIC_APP_URL',
    'NEXT_PUBLIC_SUPABASE_URL', 
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.log('❌ Missing required environment variables:');
    missingVars.forEach(varName => console.log(`   - ${varName}`));
    console.log('\nPlease set these variables in your .env file or deployment configuration.\n');
    return;
  }

  // Test Supabase connection
  try {
    console.log('🔗 Testing Supabase Connection...');
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    );

    // Test basic connection
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.log('❌ Supabase connection error:', error.message);
      return;
    }

    console.log('✅ Supabase connection successful');

    // Generate OAuth URLs for testing
    console.log('\n🔗 OAuth Redirect URLs to configure in Supabase:');
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL;
    console.log(`   - ${baseUrl}/auth/callback`);
    
    if (baseUrl.includes('localhost')) {
      console.log('   - https://your-production-domain.com/auth/callback');
    } else {
      console.log('   - http://localhost:3000/auth/callback');
    }

    console.log('\n📝 Supabase Dashboard Configuration:');
    console.log('1. Go to Authentication → Settings → General');
    console.log(`2. Set Site URL to: ${baseUrl}`);
    console.log('3. Add the redirect URLs listed above to "Redirect URLs"');
    console.log('4. Ensure Google OAuth provider is enabled in Authentication → Providers');

  } catch (error) {
    console.log('❌ Error testing Supabase:', error.message);
  }
}

// Run the test
testOAuthConfig().catch(console.error);
