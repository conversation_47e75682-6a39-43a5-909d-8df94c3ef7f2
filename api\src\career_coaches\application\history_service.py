import json
from typing import Dict, List, Optional, Any
import uuid

from common.infrastructure.supabase.client import supabase_client


class ChatHistoryService:
    """Service for managing coach chat history in Supabase."""
    
    TABLE_NAME = "coach_chat_sessions"
    
    @staticmethod
    async def save_chat_session(
        user_id: str,
        coach_id: str,
        chat_session_id: str,
        messages: List[Dict[str, Any]],
        title: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Save a chat session to Supabase.
        
        If the session already exists, it will be updated.
        
        Args:
            user_id: User ID
            coach_id: Coach ID
            chat_session_id: Unique ID for the chat session
            messages: List of message objects
            title: Optional title for the chat session
            
        Returns:
            The created or updated record
        """
        # Check if the session already exists
        existing_sessions = await supabase_client.get_records(
            ChatHistoryService.TABLE_NAME,
            {
                "user_id": user_id,
                "chat_session_id": chat_session_id,
                "is_deleted": False,
            }
        )
        
        if existing_sessions:
            # Update existing session
            session_data = {
                "messages": json.dumps(messages),
                "updated_at": "now()",
            }
            
            # Only update title if provided
            if title:
                session_data["title"] = title
                
            return await supabase_client.update_record(
                ChatHistoryService.TABLE_NAME,
                existing_sessions[0]["id"],
                session_data
            )
        else:
            # Create new session
            session_data = {
                "user_id": user_id,
                "coach_id": coach_id,
                "chat_session_id": chat_session_id,
                "messages": json.dumps(messages),
            }
            
            # Add title if provided
            if title:
                session_data["title"] = title
                
            return await supabase_client.insert_record(
                ChatHistoryService.TABLE_NAME,
                session_data
            )
    
    @staticmethod
    async def get_chat_sessions_by_user(user_id: str, coach_id: str = None) -> List[Dict[str, Any]]:
        """Get all chat sessions for a user, optionally filtered by coach.

        Args:
            user_id: User ID
            coach_id: Optional coach ID to filter by

        Returns:
            List of chat session records
        """
        filters = {
            "user_id": user_id,
            "is_deleted": False,
        }

        if coach_id:
            filters["coach_id"] = coach_id

        return await supabase_client.get_records(
            ChatHistoryService.TABLE_NAME,
            filters
        )
    
    @staticmethod
    async def get_chat_session(user_id: str, chat_session_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific chat session.
        
        Args:
            user_id: User ID
            chat_session_id: Chat session ID
            
        Returns:
            The chat session record if found, None otherwise
        """
        sessions = await supabase_client.get_records(
            ChatHistoryService.TABLE_NAME,
            {
                "user_id": user_id,
                "chat_session_id": chat_session_id,
                "is_deleted": False,
            },
            1
        )
        
        return sessions[0] if sessions else None
    
    @staticmethod
    async def delete_chat_session(user_id: str, chat_session_id: str) -> Dict[str, Any]:
        """Soft delete a chat session.
        
        Args:
            user_id: User ID
            chat_session_id: Chat session ID
            
        Returns:
            The updated record
        """
        sessions = await supabase_client.get_records(
            ChatHistoryService.TABLE_NAME,
            {
                "user_id": user_id,
                "chat_session_id": chat_session_id,
            },
            1
        )
        
        if not sessions:
            return {}
            
        return await supabase_client.update_record(
            ChatHistoryService.TABLE_NAME,
            sessions[0]["id"],
            {"is_deleted": True}
        )
    
    @staticmethod
    async def update_chat_session_title(
        user_id: str,
        chat_session_id: str,
        title: str
    ) -> Dict[str, Any]:
        """Update the title of a chat session.
        
        Args:
            user_id: User ID
            chat_session_id: Chat session ID
            title: New title
            
        Returns:
            The updated record
        """
        sessions = await supabase_client.get_records(
            ChatHistoryService.TABLE_NAME,
            {
                "user_id": user_id,
                "chat_session_id": chat_session_id,
                "is_deleted": False,
            },
            1
        )
        
        if not sessions:
            return {}
            
        return await supabase_client.update_record(
            ChatHistoryService.TABLE_NAME,
            sessions[0]["id"],
            {"title": title}
        )
