@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 168 76% 28%;
    --primary-foreground: 210 40% 98%;

    --secondary: 161 54% 47%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 168 76% 28%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 0 0% 18%;
    --sidebar-primary: 168 76% 28%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 168 76% 93%;
    --sidebar-accent-foreground: 168 76% 28%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 168 76% 28%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 168 76% 28%;
    --primary-foreground: 210 40% 98%;

    --secondary: 161 54% 47%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 168 76% 28%;

    --sidebar-background: 168 76% 10%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 168 76% 28%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 168 76% 18%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 168 76% 15%;
    --sidebar-ring: 168 76% 28%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

/* User messages */
.copilot-message.user {
  @apply my-2 max-w-[75%] self-end rounded-2xl bg-blue-100 p-3 text-blue-800;
}

/* Assistant messages */
.copilot-message.assistant {
  @apply my-2 max-w-[75%] self-start rounded-2xl bg-green-100 p-3 text-green-800;
}
