from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional

from career_coaches.application.history_service import ChatHistoryService


router = APIRouter()


class ChatSessionResponse(BaseModel):
    id: str
    user_id: str
    coach_id: str
    chat_session_id: str
    title: Optional[str] = None
    created_at: str
    updated_at: str


class ChatSessionDetailResponse(ChatSessionResponse):
    messages: list


class ChatSessionTitleRequest(BaseModel):
    title: str


@router.get("/sessions", response_model=List[ChatSessionResponse])
async def get_chat_sessions(user_id: str, coach_id: str = None):
    """Get all chat sessions for a user, optionally filtered by coach."""
    try:
        sessions = await ChatHistoryService.get_chat_sessions_by_user(user_id, coach_id)
        return sessions
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get chat sessions: {str(e)}")


@router.get("/coaches/{coach_id}/sessions", response_model=List[ChatSessionResponse])
async def get_coach_sessions(coach_id: str, user_id: str):
    """Get all chat sessions for a specific coach and user."""
    try:
        sessions = await ChatHistoryService.get_chat_sessions_by_user(user_id, coach_id)
        return sessions
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get coach sessions: {str(e)}")


@router.post("/coaches/{coach_id}/sessions")
async def create_chat_session(coach_id: str, user_id: str, title: str = None):
    """Create a new chat session for a coach."""
    try:
        import uuid
        chat_session_id = str(uuid.uuid4())

        session = await ChatHistoryService.save_chat_session(
            user_id=user_id,
            coach_id=coach_id,
            chat_session_id=chat_session_id,
            messages=[],
            title=title or f"New Chat Session"
        )
        return {"success": True, "session": session, "chat_session_id": chat_session_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create chat session: {str(e)}")


@router.get("/sessions/{chat_session_id}", response_model=ChatSessionDetailResponse)
async def get_chat_session(user_id: str, chat_session_id: str):
    """Get a specific chat session with all messages."""
    try:
        session = await ChatHistoryService.get_chat_session(user_id, chat_session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Chat session not found")
        return session
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get chat session: {str(e)}")


@router.delete("/sessions/{chat_session_id}")
async def delete_chat_session(user_id: str, chat_session_id: str):
    """Delete a chat session."""
    try:
        result = await ChatHistoryService.delete_chat_session(user_id, chat_session_id)
        if not result:
            raise HTTPException(status_code=404, detail="Chat session not found")
        return {"success": True, "message": "Chat session deleted"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete chat session: {str(e)}")


@router.patch("/sessions/{chat_session_id}/title")
async def update_chat_session_title(user_id: str, chat_session_id: str, request: ChatSessionTitleRequest):
    """Update the title of a chat session."""
    try:
        result = await ChatHistoryService.update_chat_session_title(
            user_id, chat_session_id, request.title
        )
        if not result:
            raise HTTPException(status_code=404, detail="Chat session not found")
        return {"success": True, "message": "Chat session title updated"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update chat session title: {str(e)}")
