import { Card, CardContent } from "@/components/ui/card"

interface Activity {
  id: string
  title: string
  description: string
  duration: string
  icon: string
}

interface ActivityCardProps {
  activity: Activity
}

export const ActivityCard = ({ activity }: ActivityCardProps) => {
  return (
    <Card className="cursor-pointer transition-shadow hover:shadow-md">
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 text-2xl">
              {activity.icon}
            </div>
          </div>
          <div className="flex-1">
            <div className="mb-2 flex items-center justify-between">
              <h3 className="font-semibold text-foreground">
                {activity.title}
              </h3>
              <span className="rounded bg-muted px-2 py-1 text-sm text-muted-foreground">
                {activity.duration}
              </span>
            </div>
            <p className="text-sm text-muted-foreground">
              {activity.description}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
