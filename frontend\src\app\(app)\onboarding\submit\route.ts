import { NextResponse } from "next/server"

import { createClient } from "@/lib/supabase/server"

export async function POST(request: Request) {
  const formData = await request.formData()
  const firstName = (formData.get("firstName") || "").toString().trim()
  const lastName = (formData.get("lastName") || "").toString().trim()

  if (!firstName || !lastName) {
    return NextResponse.redirect(
      new URL(
        "/onboarding?error=missing_fields",
        process.env.NEXT_PUBLIC_APP_URL
      )
    )
  }

  const supabase = await createClient()
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser()

  if (!user || userError) {
    return NextResponse.redirect(
      new URL("/signin", process.env.NEXT_PUBLIC_APP_URL)
    )
  }

  const { error: updateError } = await supabase
    .from("profiles")
    .update({
      first_name: firstName,
      last_name: lastName,
    })
    .eq("id", user.id)

  if (updateError) {
    console.error("Error updating profile:", updateError)
    return NextResponse.redirect(
      new URL(
        "/onboarding?error=update_failed",
        process.env.NEXT_PUBLIC_APP_URL
      )
    )
  }

  return NextResponse.redirect(new URL("/app", process.env.NEXT_PUBLIC_APP_URL))
}
