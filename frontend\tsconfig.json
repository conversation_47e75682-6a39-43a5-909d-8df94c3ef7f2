{"compilerOptions": {"target": "ESNext", "module": "ESNext", "lib": ["dom", "dom.iterable", "ESNext"], "allowJs": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "strict": false, "noEmit": true, "esModuleInterop": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "noUncheckedIndexedAccess": true, "baseUrl": ".", "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "contentlayer/generated": ["./.contentlayer/generated"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.mjs", "next-env.d.ts", ".next/types/**/*.ts", "src/types/**/*.d.ts", "src/types/**/*.ts", ".contentlayer/generated"], "exclude": ["node_modules"]}