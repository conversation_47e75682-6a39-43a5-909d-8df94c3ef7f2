"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import {
  getCoachById,
  getChatSessions,
  getChatSession,
  sendChatMessage,
  createChatSession,
  type Coach,
  type ChatSession
} from "@/services/careerCoachService"
import {
  Menu,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Sheet,
  SheetContent,
} from "@/components/ui/sheet"
import SessionSidebar from "@/components/careerCoach/SessionSidebar"
import CopilotChatInterface from "@/components/careerCoach/CopilotChatInterface"

// Simple UUID generation for demo purposes
// In production, this should come from authentication
const generateUserId = () => {
  if (typeof window !== 'undefined') {
    let userId = localStorage.getItem('demo_user_id')
    if (!userId) {
      userId = crypto.randomUUID()
      localStorage.setItem('demo_user_id', userId)
    }
    return userId
  }
  return crypto.randomUUID()
}

interface ChatMessageType {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
}

// ChatSession interface is now imported from the service

export default function ChatSessionPage() {
  const params = useParams()
  const router = useRouter()
  const coachId = params.coach_id as string
  const sessionId = params.session_id as string

  const [coach, setCoach] = useState<Coach | null>(null)
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null)
  const [sessions, setSessions] = useState<ChatSession[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [sidebarOpen, setSidebarOpen] = useState(false)

  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true)
        
        // Load coach details
        const coachData = await getCoachById(coachId)
        if (!coachData) {
          setError("Coach not found")
          return
        }
        setCoach(coachData)

        // Load current session and messages
        const userId = generateUserId()
        const currentSessionData = await getChatSession(userId, sessionId)

        if (currentSessionData) {
          // Existing session found
          setCurrentSession(currentSessionData)
        } else {
          // New session - create a mock session object for UI purposes
          const newSession: ChatSession = {
            id: sessionId,
            user_id: userId,
            coach_id: coachId,
            chat_session_id: sessionId,
            title: "New Chat Session",
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }
          setCurrentSession(newSession)
        }

        // Load session history
        const sessionsData = await getChatSessions(userId, coachId)
        setSessions(sessionsData)
        setError(null)
      } catch (err) {
        setError("Failed to load session data")
        console.error("Error loading session:", err)
      } finally {
        setLoading(false)
      }
    }

    if (coachId && sessionId) {
      loadData()
    }
  }, [coachId, sessionId])

  const handleCreateSession = async (title?: string) => {
    try {
      const userId = generateUserId()
      const result = await createChatSession(
        coachId,
        userId,
        title || "New Chat Session"
      )

      if (result) {
        router.push(`/app/careerCoach/${coachId}/${result.chat_session_id}`)
      }
    } catch (err) {
      console.error("Error creating session:", err)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return "Yesterday"
    if (diffDays < 7) return `${diffDays} days ago`
    return date.toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto mb-4 size-12 animate-spin rounded-full border-4 border-blue-100 border-t-blue-600"></div>
          <p className="text-lg text-gray-500">Loading your session...</p>
        </div>
      </div>
    )
  }

  if (error || !coach || !currentSession) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 p-6">
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold text-red-600 mb-2">
              Something went wrong
            </h2>
            <p className="text-gray-600 mb-4">{error || "Session not found"}</p>
            <Button
              onClick={() => router.push("/app/careerCoach")}
              className="w-full"
            >
              Return to Career Coach
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }



  return (
    <div className="flex h-screen bg-gray-50 overflow-hidden">
      {/* Desktop Sidebar */}
      <div className="hidden md:block w-80 h-screen overflow-hidden">
        <SessionSidebar
          coach={coach}
          sessions={sessions}
          currentSessionId={sessionId}
          onCreateSession={handleCreateSession}
        />
      </div>

      {/* Mobile Sidebar */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetContent side="left" className="w-80 p-0">
          <SessionSidebar
            coach={coach}
            sessions={sessions}
            currentSessionId={sessionId}
            onCreateSession={handleCreateSession}
          />
        </SheetContent>
      </Sheet>

      {/* Main Chat Area */}
      <div className="flex flex-1 flex-col h-screen overflow-hidden">
        {/* Chat Header */}
        <div className="border-b bg-white p-4 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="size-4" />
              </Button>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  {currentSession.title}
                </h1>
                <p className="text-sm text-gray-600">
                  with {coach.name}
                </p>
              </div>
            </div>
            <Badge variant="outline">Active Session</Badge>
          </div>
        </div>

        {/* Copilot Chat Interface */}
        <div className="flex-1 overflow-hidden">
          <CopilotChatInterface
            coachId={coachId}
            sessionId={sessionId}
            userId={generateUserId()}
            coachName={coach.name}
            coachSpecialty={coach.specialty}
            onMessageSent={(message) => {
              console.log("Message sent:", message)
              // Handle message sent if needed
            }}
          />
        </div>
      </div>
    </div>
  )
}
