import { useState } from "react"
import { v4 as uuidv4 } from "uuid"
import { FileText, Download, RefreshCw, Eye, AlertCircle } from "lucide-react"
import { createClient } from "@/lib/supabase/client"

import type { Application, Document, DocumentStatus } from "@/types/application"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface TailorHistoryRowProps {
  record: Application
  onRefresh: () => Promise<void>
}

export function TailorHistoryRow({ record, onRefresh }: TailorHistoryRowProps) {
  const [showDetails, setShowDetails] = useState(false)

  const getStatusBadge = (status: Application["status"]) => {
    switch (status) {
      case "Completed":
        return (
          <Badge className="border-0 bg-green-100 text-green-700">
            Completed
          </Badge>
        )
      case "In Progress":
        return (
          <Badge className="border-0 bg-blue-100 text-blue-700">
            In Progress
          </Badge>
        )
      case "Partially Completed":
        return (
          <Badge className="border-0 bg-yellow-100 text-yellow-700">
            Partially Completed
          </Badge>
        )
      case "Pending":
        return (
          <Badge className="border-0 bg-gray-100 text-gray-700">
            Pending
          </Badge>
        )
      case "Failed":
        return (
          <Badge className="border-0 bg-red-100 text-red-700">Failed</Badge>
        )
      default:
        return (
          <Badge className="border-0 bg-gray-100 text-gray-700">{status}</Badge>
        )
    }
  }
  
  const getDocumentStatusBadge = (status: DocumentStatus) => {
    switch (status) {
      case "completed":
        return (
          <Badge className="border-0 bg-green-100 text-green-700">
            Completed
          </Badge>
        )
      case "processing":
        return (
          <Badge className="border-0 bg-blue-100 text-blue-700">
            Processing
          </Badge>
        )
      case "pending":
        return (
          <Badge className="border-0 bg-gray-100 text-gray-700">
            Pending
          </Badge>
        )
      case "failed":
        return (
          <Badge className="border-0 bg-red-100 text-red-700">Failed</Badge>
        )
      default:
        return (
          <Badge className="border-0 bg-gray-100 text-gray-700">{status}</Badge>
        )
    }
  }
  
  // Calculate progress based on document statuses
  const calculateProgress = () => {
    if (!record.documents || record.documents.length === 0) return 0
    
    const total = record.documents.length
    const completed = record.documents.filter(doc => doc.status === "completed").length
    const processing = record.documents.filter(doc => doc.status === "processing").length
    
    // Each processing document counts as 50% progress
    return Math.round(((completed + (processing * 0.5)) / total) * 100)
  }
  
  // Handle document regeneration
  const handleRegenerateDocument = async (document: Document) => {
    try {
      const supabase = createClient()
      const requestId = uuidv4()
      
      // Update document status to pending
      await supabase
        .from("tailored_documents")
        .update({ 
          status: "pending", 
          request_id: requestId,
          updated_at: new Date().toISOString()
        })
        .eq("id", document.id)
      
      // Call the edge function to regenerate the document
      const { error: funcError } = await supabase.functions.invoke('process-tailoring-request', {
        body: JSON.stringify({
          applicationId: record.id,
          resumeId: record.resume_id,
          jobDescription: record.job.job_description,
          companyName: record.company.name,
          jobTitle: record.job.job_title,
          generateResume: document.document_type === 'resume',
          generateCoverLetter: document.document_type === 'cover_letter',
          requestId,
        }),
      })
      
      if (funcError) {
        console.error('Error invoking edge function:', funcError)
      }
      
      // Refresh the data
      await onRefresh()
    } catch (error) {
      console.error("Error regenerating document:", error)
    }
  }

  // const getMatchScoreColor = (score: number | null) => {
  //   if (score === null) return "text-gray-600 bg-gray-100"
  //   if (score >= 80) return "text-green-600 bg-green-100"
  //   if (score >= 60) return "text-yellow-600 bg-yellow-100"
  //   return "text-red-600 bg-red-100"
  // }

  return (
    <>
      <tr
        className="cursor-pointer hover:bg-gray-50"
        onClick={() => setShowDetails(true)}
      >
        <td className="px-4 py-3 text-sm font-medium text-gray-900">
          {record.job.job_title}
        </td>
        <td className="px-4 py-3 text-sm text-gray-600">
          {record.company.name}
        </td>
        <td className="px-4 py-3 text-sm text-gray-600">
          {new Date(record.applied_date).toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          })}
        </td>
        {/* <td className="px-4 py-3 text-sm text-gray-600">
          {record.resume.resume_name}
        </td> */}
        <td className="px-4 py-3">
          <div className="flex flex-col gap-1">
            {getStatusBadge(record.status)}
            {record.documents && record.documents.length > 0 && record.status === "In Progress" && (
              <div className="mt-1 w-full">
                <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                  <div 
                    className="h-full bg-blue-500 transition-all duration-500" 
                    style={{ width: `${calculateProgress()}%` }} 
                  />
                </div>
                <span className="mt-1 text-xs text-gray-600">{calculateProgress()}%</span>
              </div>
            )}
          </div>
        </td>
        <td className="px-4 py-3">
          <div className="flex items-center gap-2">
            {record.documents && record.documents.map((doc) => {
              const isResume = doc.document_type === 'resume'
              const isCoverLetter = doc.document_type === 'cover_letter'
              const isCompleted = doc.status === 'completed'
              const isFailed = doc.status === 'failed'
              
              return (
                <div key={doc.id} className="flex items-center gap-1">
                  {isCompleted && doc.file_url && isResume && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="size-8 p-0"
                      title="Download tailored resume"
                      onClick={(e) => {
                        e.stopPropagation()
                        window.open(doc.file_url, "_blank")
                      }}
                    >
                      <Download className="size-4 text-gray-600" />
                    </Button>
                  )}
                  
                  {isCompleted && isCoverLetter && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="size-8 p-0"
                      title="View cover letter"
                      onClick={(e) => {
                        e.stopPropagation()
                        setShowDetails(true)
                      }}
                    >
                      <Eye className="size-4 text-gray-600" />
                    </Button>
                  )}
                  
                  {(isCompleted || isFailed) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="size-8 p-0"
                      title="Regenerate document"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleRegenerateDocument(doc)
                      }}
                    >
                      <RefreshCw className="size-4 text-gray-600" />
                    </Button>
                  )}
                  
                  {isFailed && (
                    <div className="relative group">
                      <AlertCircle className="size-4 text-red-500" aria-label={doc.error_message || 'Generation failed'} />
                      <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        {doc.error_message || 'Generation failed'}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </td>
      </tr>

      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Job Details</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Header Section */}
            <div className="flex items-start justify-between border-b pb-4">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">
                  {record.job.job_title}
                </h3>
                <p className="mt-1 text-gray-600">{record.company.name}</p>
              </div>
            </div>

            {/* Job Description */}
            <div>
              <h4 className="mb-2 font-medium text-gray-900">
                Job Description
              </h4>
              <div className="whitespace-pre-wrap rounded-lg border border-gray-200 bg-gray-50 p-4 text-sm text-gray-700">
                {record.job.job_description}
              </div>
            </div>

            {/* Company Description */}
            {record.company.name && (
              <div>
                <h4 className="mb-2 font-medium text-gray-900">
                  About {record.company.name}
                </h4>
                <div className="whitespace-pre-wrap rounded-lg border border-gray-200 bg-gray-50 p-4 text-sm text-gray-700">
                  {record.company.name}
                </div>
              </div>
            )}

            {/* Generated Files */}
            {record.documents && record.documents.length > 0 && (
              <div>
                <h4 className="mb-2 font-medium text-gray-900">
                  Generated Documents
                </h4>
                <div className="space-y-4">
                  {record.documents.map(doc => (
                    <div key={doc.id} className="rounded-lg border border-gray-200 p-4">
                      <div className="mb-2 flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <FileText className="size-4" />
                          <span className="font-medium">
                            {doc.document_type === 'resume' ? 'Tailored Resume' : 'Cover Letter'}
                          </span>
                          {getDocumentStatusBadge(doc.status)}
                        </div>
                        <div className="flex items-center gap-2">
                          {doc.status === 'completed' && doc.document_type === 'resume' && doc.file_url && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.open(doc.file_url, "_blank")}
                            >
                              <Download className="mr-2 size-4" />
                              Download
                            </Button>
                          )}
                          {(doc.status === 'completed' || doc.status === 'failed') && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleRegenerateDocument(doc)}
                            >
                              <RefreshCw className="mr-2 size-4" />
                              Regenerate
                            </Button>
                          )}
                        </div>
                      </div>
                      
                      {doc.status === 'processing' && (
                        <div className="mt-2">
                          <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                            <div className="h-full animate-pulse bg-blue-500" style={{ width: '60%' }} />
                          </div>
                          <p className="mt-1 text-xs text-gray-500">Processing... This may take about a minute.</p>
                        </div>
                      )}
                      
                      {doc.status === 'failed' && (
                        <div className="mt-2 rounded-md bg-red-50 p-3 text-sm text-red-600">
                          {doc.error_message || 'An error occurred during document generation. Please try regenerating.'}
                        </div>
                      )}
                      
                      {doc.status === 'completed' && doc.document_type === 'cover_letter' && doc.content && (
                        <div className="mt-2">
                          <div className="max-h-60 overflow-y-auto rounded-md bg-gray-50 p-3 text-sm">
                            <div className="whitespace-pre-wrap">{doc.content}</div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
