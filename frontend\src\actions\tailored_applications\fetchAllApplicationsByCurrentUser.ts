"use server"

import type { Application } from "@/types/application"

import { createClient } from "@/lib/supabase/server"

class ApplicationError extends Error {
  constructor(
    message: string,
    public cause?: Error
  ) {
    super(message)
  }
}

/**
 * Fetches all applications for a specific user from the database
 * @returns Promise resolving to an array of applications
 * @throws {ApplicationError} When the applications cannot be fetched or user is invalid
 */
export async function fetchAllApplicationsByCurrentUser(): Promise<
  Application[]
> {
  try {
    const supabase = createClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      throw new ApplicationError("Failed to fetch user", authError)
    }

    const { data, error: applicationError } = await supabase
      .from("applications")
      .select(
        `
        *,
        job:jobs (
          id,
          job_title,
          job_description
        ),
        company:companies ( 
          id,
          name
        ),
        documents:tailored_documents (
          id,
          document_type,
          status,
          file_url,
          content,
          created_at,
          updated_at,
          completed_at,
          error_message,
          request_id
        )
        `
      )
      .eq("user_id", user.id)
      .order("created_at", { ascending: false })

    if (applicationError) {
      throw new ApplicationError(
        "Failed to fetch applications",
        applicationError
      )
    }

    if (!data) {
      return []
    }

    if (!Array.isArray(data)) {
      throw new ApplicationError(
        "Invalid data structure received",
        new Error("Invalid data structure received")
      )
    }

    const validApplications = data.filter((item): item is Application => {
      const isValid = isValidApplication(item)
      if (!isValid) {
        console.warn("Invalid application data found:", item)
      }

      return isValid
    })

    return validApplications
  } catch (error) {
    console.error("Error in fetchAllApplicationsByCurrentUser:", error)
    throw error instanceof ApplicationError
      ? error
      : new ApplicationError(
          "Unexpected error while fetching applications",
          error as Error
        )
  }
}

/**
 * Type guard to validate the application data structure
 * @param data - The data to validate
 * @returns boolean indicating if the data is a valid Application
 */
function isValidApplication(data: any): data is Application {
  // Check for basic structure of application
  const hasBasicStructure = (
    typeof data === "object" &&
    data !== null &&
    typeof data.id === "string" &&
    typeof data.user_id === "string" &&
    typeof data.created_at === "string" &&
    typeof data.updated_at === "string" &&
    typeof data.status === "string" &&
    typeof data.job_id === "string" &&
    typeof data.company_id === "string" &&
    typeof data.resume_id === "string" &&
    // applied_date can be string or missing
    (typeof data.applied_date === "string" || data.applied_date === undefined) &&
    // notes can be null or string
    (data.notes === null || typeof data.notes === "string" || data.notes === undefined)
  );
  
  if (!hasBasicStructure) return false;
  
  // Check job details
  const hasValidJob = (
    typeof data.job === "object" &&
    data.job !== null &&
    typeof data.job.id === "string" &&
    typeof data.job.job_title === "string" &&
    typeof data.job.job_description === "string"
  );
  
  if (!hasValidJob) return false;
  
  // Check company details
  const hasValidCompany = (
    typeof data.company === "object" &&
    data.company !== null &&
    typeof data.company.id === "string" &&
    typeof data.company.name === "string"
  );
  
  // Allow documents array to be optional or have document objects
  const hasValidDocuments = !data.documents || (
    Array.isArray(data.documents) && 
    data.documents.every((doc: any) => 
      typeof doc === "object" && 
      doc !== null &&
      typeof doc.id === "string" &&
      typeof doc.document_type === "string" &&
      typeof doc.status === "string"
    )
  );
  
  return hasValidCompany && hasValidDocuments;
}
