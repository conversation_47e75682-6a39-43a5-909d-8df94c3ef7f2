"use client"

import * as React from "react"
import { useRouter, usePathname } from "next/navigation"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import type { Provider } from "@supabase/supabase-js"

import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { useToast } from "@/hooks/use-toast"

// Only include OAuth providers that are actually supported by our app
type SupportedOAuthProvider = Extract<Provider, "google" | "github">

interface OAuthButtonsProps {
  // Optional providers to display. If not provided, all available providers will be shown.
  providers?: SupportedOAuthProvider[]
  // Optional size for the buttons
  size?: "default" | "sm" | "lg"
  // Optional alignment for icon and text
  align?: "center" | "start"
}

export function OAuthButtons({ 
  providers = ["google"], 
  size = "default",
  align = "center"
}: OAuthButtonsProps) {
  const supabase = createClientComponentClient()
  const { toast } = useToast()
  const router = useRouter()
  const pathname = usePathname()
  const [isLoading, setIsLoading] = React.useState<SupportedOAuthProvider | null>(null)

  const isSignIn = pathname.includes("/signin")
  const buttonText = isSignIn ? "Sign in with" : "Continue with"

  const handleOAuthSignIn = async (provider: SupportedOAuthProvider) => {
    try {
      setIsLoading(provider)

      // Get the redirect URL - prefer environment variable, fallback to window.location.origin
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000')
      const redirectTo = `${baseUrl}/auth/callback`

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      })
      
      if (error) {
        toast({
          title: "Authentication Error",
          description: `There was a problem with ${provider} authentication`,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error(error)
      toast({
        title: "Something went wrong",
        description: "Please try again later",
        variant: "destructive",
      })
    } finally {
      setIsLoading(null)
    }
  }

  const getProviderIcon = (provider: SupportedOAuthProvider) => {
    switch (provider) {
      case "google":
        return <Icons.google className="size-4" aria-hidden="true" />
      case "github":
        return <Icons.gitHub className="size-4" aria-hidden="true" />
      default:
        return <Icons.user className="size-4" aria-hidden="true" />;
    }
  }

  const buttonAlignClass = align === "center" ? "justify-center" : "justify-start"

  return (
    <div className="grid gap-2 w-full">
      {providers.map((provider) => (
        <Button
          key={provider}
          variant="outline"
          size={size}
          onClick={() => handleOAuthSignIn(provider)}
          disabled={isLoading !== null}
          className={`flex w-full items-center gap-2 ${buttonAlignClass} relative hover:bg-secondary/30`}
        >
          {isLoading === provider ? (
            <Icons.spinner className="mr-2 size-4 animate-spin" aria-hidden="true" />
          ) : (
            getProviderIcon(provider)
          )}
          <span className="flex-1 text-center">{buttonText} {provider.charAt(0).toUpperCase() + provider.slice(1)}</span>
        </Button>
      ))}
    </div>
  )
}
