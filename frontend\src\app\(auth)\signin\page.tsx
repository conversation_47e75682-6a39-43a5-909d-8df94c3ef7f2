"use client"

import { Suspense } from "react"
import Link from "next/link"

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { SignInWithPasswordForm } from "@/components/forms/signin-with-password-form"
import { Icons } from "@/components/icons"
import { OAuthButtons } from "@/components/oauth-buttons"

export default function SignInPage() {
  return (
    <div className="flex h-auto min-h-screen w-full items-center justify-center bg-secondary/20">
      <Card className="w-full max-w-[420px] sm:rounded-lg sm:border sm:shadow-lg">
        <CardHeader className="space-y-1 pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold text-primary">Sign in</CardTitle>
            <Link href="/" className="rounded-full p-1 hover:bg-secondary transition-colors">
              <Icons.close className="size-4" />
            </Link>
          </div>
          <CardDescription className="text-sm">
            Sign in to your InternUp account
          </CardDescription>
        </CardHeader>

        <CardContent className="w-full px-6 sm:px-8">
          <OAuthButtons />
          
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative mb-3 mt-6 flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or continue with password
              </span>
            </div>
          </div>
          
          <Suspense fallback={<div className="w-full py-6 text-center">Loading sign in form...</div>}>
            <SignInWithPasswordForm />
          </Suspense>
        </CardContent>

        <CardFooter className="flex flex-col w-full gap-2 px-6 text-sm text-muted-foreground sm:px-8">
          <p className="sr-only">
            Only InternUp VIP users are allowed access to sign in
          </p>
          <div className="w-full flex justify-between items-center">
            <Link
              href="/signin/password-reset"
              className="text-sm text-primary hover:underline transition-all"
            >
              Forgot password?
            </Link>
            
            <Link
              aria-label="Sign up"
              href="/signup"
              className="font-semibold text-primary underline-offset-4 hover:underline transition-all"
            >
              Create account
            </Link>
          </div>
          <p className="text-xs mt-4 text-center">
            Need help? Contact us at{" "}
            <a href="mailto:<EMAIL>" className="underline hover:text-primary transition-colors">
              <EMAIL>
            </a>
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}
