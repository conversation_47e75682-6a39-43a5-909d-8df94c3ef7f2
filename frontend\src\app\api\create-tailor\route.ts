import { NextResponse } from "next/server"
import { v4 as uuidv4 } from "uuid"
import { createApplication } from "@/actions/tailored_applications/createApplication"
import { createClient } from "@/lib/supabase/server"

export async function POST(req: Request) {
  try {
    const formData = await req.formData()

    // Get form data values
    const resumeId = formData.get("resumeId")
    const jobTitle = formData.get("jobTitle")
    const jobDescription = formData.get("jobDescription")
    const jobUrl = formData.get("jobUrl")
    const companyName = formData.get("companyName")
    const companyDescription = formData.get("companyDescription")
    const shouldTailorResume = formData.get("shouldTailorResume") === "true"
    const generateCoverLetter = formData.get("generateCoverLetter") === "true"

    // Validate required fields
    if (!resumeId || !jobTitle || !jobDescription || !companyName) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required fields",
        },
        { status: 400 }
      )
    }

    console.log("📝 API received data:", {
      resumeId,
      jobTitle,
      jobDescription: jobDescription.toString().substring(0, 100) + "...",
      jobUrl,
      companyName,
      shouldTailorResume,
      generateCoverLetter,
    })

    // Generate a unique request ID for this tailoring request
    const requestId = uuidv4();
    
    const result = await createApplication({
      resumeId: resumeId.toString(),
      jobTitle: jobTitle.toString(),
      jobDescription: jobDescription.toString(),
      jobUrl: jobUrl?.toString() || undefined,
      companyName: companyName.toString(),
      companyDescription: companyDescription?.toString() || undefined,
      shouldTailorResume,
      generateCoverLetter,
      requestId,
    })
    
    // If application creation was successful and documents need to be generated,
    // invoke the edge function to process the request in the background
    if (result.success && result.data?.application && (shouldTailorResume || generateCoverLetter)) {
      try {
        // Initialize Supabase client
        const supabase = createClient();
        
        // Call the edge function
        const { data: funcData, error: funcError } = await supabase.functions.invoke('process-tailoring-request', {
          body: JSON.stringify({
            applicationId: result.data.application.id,
            resumeId: resumeId.toString(),
            jobDescription: jobDescription.toString(),
            companyName: companyName.toString(),
            jobTitle: jobTitle.toString(),
            generateResume: shouldTailorResume,
            generateCoverLetter,
            requestId,
          }),
        });
        
        if (funcError) {
          console.error('Error invoking edge function:', funcError);
          // We'll continue even if there's an error, as the documents are already created in pending state
        } else {
          console.log('Edge function invoked successfully:', funcData);
        }
      } catch (error) {
        console.error('Error invoking edge function:', error);
        // We'll continue even if there's an error, as the documents are already created in pending state
      }
    }

    console.log("✅ API operation result:", result)
    return NextResponse.json(result)
  } catch (error) {
    console.error("❌ Error in create-tailor API:", error)
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to create tailored application",
      },
      { status: 500 }
    )
  }
}
