{"cells": [{"cell_type": "code", "execution_count": 17, "id": "15a5259a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tavily API Key: ✅ Available\n", "Serper API Key: ✅ Available\n", "DuckDuckGo: ✅ Available (no API key needed)\n"]}], "source": ["# Import necessary libraries and fix path\n", "import os\n", "import sys\n", "import asyncio\n", "from typing import List, Dict\n", "\n", "# Add the parent directory to Python path so modules can be found\n", "# This is critical to solve the ModuleNotFoundError\n", "sys.path.append(os.path.abspath('..'))\n", "\n", "# For <PERSON><PERSON><PERSON> notebooks\n", "import nest_asyncio\n", "nest_asyncio.apply()\n", "\n", "# Import the WebEnabledCareerCoach class from our experiment file\n", "from career_coach_web_search_experiment import (\n", "    WebEnabledCareerCoach,\n", "    test_search_tool,\n", "    # setup_search_tools\n", ")\n", "\n", "# Load environment variables for API keys\n", "from dotenv import load_dotenv\n", "load_dotenv()  # Load from .env file\n", "\n", "# Check and print available API keys (without showing the actual keys)\n", "print(\"Tavily API Key:\", \"✅ Available\" if os.getenv(\"TAVILY_API_KEY\") else \"❌ Missing\")\n", "print(\"Serper API Key:\", \"✅ Available\" if os.getenv(\"SERPER_API_KEY\") or os.getenv(\"SERPAPI_API_KEY\") else \"❌ Missing\")\n", "print(\"DuckDuckGo:\", \"✅ Available (no API key needed)\")\n", "\n", "# Setup search tools\n", "# tools = setup_search_tools()\n", "\n", "# Create different coach instances for testing\n", "tavily_coach = WebEnabledCareerCoach(search_tool_name=\"tavily\", coach_type=\"career_assessment\")\n", "# serper_coach = WebEnabledCareerCoach(search_tool_name=\"serper\", coach_type=\"career_assessment\") \n", "ddg_coach = WebEnabledCareerCoach(search_tool_name=\"ddg\", coach_type=\"career_assessment\")\n", "all_tools_coach = WebEnabledCareerCoach(search_tool_name=\"all\", coach_type=\"career_assessment\")\n", "\n", "# Function to test with different coaches\n", "def test_question(question, coach_name=\"tavily\"):\n", "    \"\"\"Test a question with a specific coach\"\"\"\n", "    print(f\"\\n🔍 Testing with {coach_name} coach: '{question}'\")\n", "    \n", "    if coach_name == \"tavily\":\n", "        coach = tavily_coach\n", "    elif coach_name == \"serper\":\n", "        coach = serper_coach\n", "    elif coach_name == \"ddg\":\n", "        coach = ddg_coach\n", "    else:  # \"all\"\n", "        coach = all_tools_coach\n", "    \n", "    response = coach.ask_with_web_search(question)\n", "    return response\n", "\n", "# Test with a sample question\n", "sample_question = \"What are the current trends in tech hiring for entry-level positions in 2025?\"\n", "# response = test_question(sample_question, \"tavily\")  # Uncomment to test"]}, {"cell_type": "code", "execution_count": 25, "id": "fc59b205", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 Testing with tavily coach: 'What are the current trends in tech hiring for entry-level positions in 2025?'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["OPIK: Started logging traces to the \"Default Project\" project at https://www.comet.com/opik/api/v1/session/redirect/projects/?trace_id=01973ba3-0a58-7aca-835e-e1aa501085e3&path=aHR0cHM6Ly93d3cuY29tZXQuY29tL29waWsvYXBpLw==.\n"]}], "source": ["response = test_question(sample_question, \"tavily\")"]}, {"cell_type": "code", "execution_count": 26, "id": "ade99793", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I'm so excited to chat with you about the current trends in tech hiring. As a career coach, I've been keeping a close eye on the market, and I must say, it's an incredible time to be entering the tech industry.\n", "\n", "Before we dive into the trends, I just want to say that I'm thrilled to have you here, and I'm looking forward to exploring your passions and strengths together. \n", "\n", "To get us started, can you tell me a little bit about yourself, such as your name and what brings you to this coaching session today?\n"]}], "source": ["print(response)  "]}, {"cell_type": "code", "execution_count": null, "id": "d9ffa2d0", "metadata": {}, "outputs": [], "source": ["# ===== Main Testing Code =====\n", "\n", "# Uncomment these lines to test individual search tools\n", "'''\n", "print(\"Testing search tools directly...\\n\")\n", "\n", "search_query = \"current tech job market trends 2025\"\n", "\n", "if tavily_search:\n", "    test_search_tool(tavily_search, \"Tavily Search\", search_query)\n", "    \n", "if serper_search:\n", "    test_search_tool(serper_search, \"Serper API\", search_query)\n", "\n", "test_search_tool(ddg_search, \"DuckDuckGo Search\", search_query)\n", "'''\n", "\n", "# Test career coach with different search tools\n", "'''\n", "async def run_tests():\n", "    coach_types = [\"career_assessment\", \"resume_builder\", \"linkedin_optimizer\", \"networking_strategy\"]\n", "    search_tools = []\n", "    \n", "    if tavily_search:\n", "        search_tools.append((tavily_search, \"Tavily Search\"))\n", "        \n", "    if serper_search:\n", "        search_tools.append((serper_search, \"Serper API\"))\n", "    \n", "    search_tools.append((ddg_search, \"DuckDuckGo Search\"))\n", "    \n", "    # Test each combination of coach type and search tool\n", "    for coach_type in coach_types:\n", "        for tool, tool_name in search_tools:\n", "            await test_career_coach_with_search(coach_type, tool, tool_name)\n", "            \n", "# asyncio.run(run_tests())\n", "'''\n", "\n", "print(\"\"\"\n", "To test the web search tools:\n", "1. Create a .env file with your API keys:\n", "   TAVILY_API_KEY=your_tavily_api_key\n", "   SERPER_API_KEY=your_serper_api_key\n", "   \n", "2. Uncomment the testing code at the bottom of this file\n", "3. Run this file as a script or in a Jupyter notebook\n", "\"\"\")\n", "\n", "# Example usage:\n", "# coach = WebEnabledCareerCoach(coach_type=\"career_assessment\", search_tool=tavily_search, search_tool_name=\"<PERSON><PERSON>\")\n", "# response = coach.ask_with_web_search(\"What are the latest trends in tech hiring?\")\n", "# print(response)\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}