/** @type {import("eslint").Linter.Config} */
{
  "root": true,
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "project": true,
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "plugins": ["@typescript-eslint", "tailwindcss"],
  "extends": [
    "next/core-web-vitals",
    "plugin:tailwindcss/recommended",
    "prettier"
  ],
  "rules": {
    "@typescript-eslint/consistent-type-imports": [
      "warn",
      {
        "prefer": "type-imports",
        "fixStyle": "inline-type-imports"
      }
    ],
    "@typescript-eslint/no-unused-vars": [
      "warn",
      { 
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_"
      }
    ],
    "@typescript-eslint/no-misused-promises": [
      "error",
      {
        "checksVoidReturn": false
      }
    ],
    "@typescript-eslint/consistent-type-definitions": ["error","interface"],
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/no-unnecessary-condition": "off",
    "@typescript-eslint/restrict-template-expressions": "off",
    "@typescript-eslint/no-confusing-void-expression": "off",
    "@typescript-eslint/no-explicit-any": 0,
    "@next/next/no-img-element": "off",
    "tailwindcss/no-custom-classname": "off"
  },
  "settings": {
    "next": {
      "rootDir": ["./"]
    },
    "tailwindcss": {
      "callees": ["cn", "cva"],
      "config": "./tailwind.config.ts"
      // "classRegex": "^(class(Name)?|tw)$"
    }
  },
  "ignorePatterns": [
    ".eslintrc.json",
    "node_modules/",
    ".next/",
    "build/",
    "public/"
  ]
}
