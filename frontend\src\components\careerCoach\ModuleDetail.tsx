import { ArrowLeft } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

interface Activity {
  id: string
  title: string
  description: string
  duration: string
  icon: string
}

interface ModuleDetailProps {
  title: string
  description: string
  imageSrc: string
  activities: Activity[]
  onBack: () => void
}

export default function ModuleDetail({
  title,
  description,
  imageSrc,
  activities,
  onBack,
}: ModuleDetailProps) {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-white px-6 py-4">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={onBack}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
          <div className="ml-auto flex items-center space-x-4">
            <span className="font-medium text-foreground">
              Help me find an activity
            </span>
            <Button variant="outline" size="sm">
              🔄 Restart
            </Button>
          </div>
        </div>
      </header>

      <div className="mx-auto max-w-4xl p-6">
        {/* Module Header */}
        <div className="mb-8">
          <div className="mb-6 rounded-lg bg-green-100 p-8">
            <img
              src={imageSrc}
              alt={title}
              className="h-48 w-full rounded-lg object-cover"
            />
          </div>
          <h1 className="mb-4 text-3xl font-bold text-foreground">{title}</h1>
          <p className="text-lg leading-relaxed text-muted-foreground">
            {description}
          </p>
        </div>

        {/* Activities Section */}
        <div>
          <h2 className="mb-6 text-xl font-semibold text-foreground">
            Activities in this Module
          </h2>
          <div className="space-y-4">
            {activities.map((activity) => (
              <Card
                key={activity.id}
                className="cursor-pointer transition-shadow hover:shadow-md"
              >
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100 text-2xl">
                        {activity.icon}
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="mb-2 flex items-center justify-between">
                        <h3 className="font-semibold text-foreground">
                          {activity.title}
                        </h3>
                        <span className="rounded bg-muted px-3 py-1 text-sm text-muted-foreground">
                          {activity.duration}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {activity.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Similar Modules Section */}
        <div className="mt-12">
          <h2 className="mb-6 text-xl font-semibold text-orange-500">
            Similar modules
          </h2>
          <Card className="cursor-pointer transition-shadow hover:shadow-md">
            <CardContent className="p-6">
              <div className="flex items-start space-x-6">
                <div className="h-16 w-24 flex-shrink-0 rounded-lg bg-blue-100"></div>
                <div className="flex-1">
                  <div className="mb-2 flex items-center space-x-2">
                    <span className="rounded bg-muted px-2 py-1 text-sm text-muted-foreground">
                      Module
                    </span>
                    <span className="rounded bg-muted px-2 py-1 text-sm text-muted-foreground">
                      65-115 min
                    </span>
                  </div>
                  <h3 className="mb-2 font-semibold text-foreground">
                    Prepare for a job interview
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Sharpen your interview skills to make a lasting impression
                    on potential employers. This module combines crafting your
                    elevator pitch with practical interview practices and tips.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
