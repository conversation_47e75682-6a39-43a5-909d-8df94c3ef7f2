# AWS Deployment Makefile for SophieAI
# This Makefile provides a streamlined process for deploying frontend and backend to AWS

# Configuration - Override these values using environment variables or command line args
AWS_REGION ?= us-east-1
ECR_REPO_NAME ?= sophieai-backend
AMPLIFY_APP_ID ?= d2k7iaz22pjvy2
FRONTEND_DIR ?= $(CURDIR)/../frontend
BACKEND_DIR ?= $(CURDIR)/../api
APP_RUNNER_SERVICE_NAME ?= sophieai-backend-service
BACKEND_IMAGE_TAG ?= latest
BRANCH_NAME ?= main

# Ensure scripts are executable
SCRIPTS_DIR := $(CURDIR)/scripts
BACKEND_DEPLOY_SCRIPT := $(SCRIPTS_DIR)/deploy_backend.sh
FRONTEND_DEPLOY_SCRIPT := $(SCRIPTS_DIR)/deploy_frontend.sh

.PHONY: help
help:
	@echo "SophieAI AWS Deployment Makefile"
	@echo ""
	@echo "Usage:"
	@echo "  make setup                  Setup AWS resources (one-time)"
	@echo "  make deploy-frontend        Deploy frontend to Amplify"
	@echo "  make build-backend          Build backend Docker image"
	@echo "  make push-backend           Push backend image to ECR"
	@echo "  make deploy-backend         Deploy backend to App Runner"
	@echo "  make deploy                 Deploy both frontend and backend"
	@echo "  make clean                  Clean up local build artifacts"
	@echo ""
	@echo "Configuration (override with environment variables):"
	@echo "  AWS_REGION                  AWS region (default: us-east-1)"
	@echo "  ECR_REPO_NAME               ECR repository name (default: sophieai-backend)"
	@echo "  AMPLIFY_APP_ID              Amplify app ID (REQUIRED - no default)"
	@echo "  FRONTEND_DIR                Frontend directory path (default: ../frontend)"
	@echo "  BACKEND_DIR                 Backend directory path (default: ../api)"
	@echo "  APP_RUNNER_SERVICE_NAME     App Runner service name (default: sophieai-backend-service)"
	@echo "  BACKEND_IMAGE_TAG           Image tag for ECR (default: latest)"
	@echo "  BRANCH_NAME                 Git branch name for Amplify (default: main)"

.PHONY: ensure-scripts-executable
ensure-scripts-executable:
	@chmod +x $(BACKEND_DEPLOY_SCRIPT) $(FRONTEND_DEPLOY_SCRIPT)
	@echo "Scripts are now executable"

.PHONY: validate-config
validate-config:
	@if [ -z "$(AMPLIFY_APP_ID)" ] || [ "$(AMPLIFY_APP_ID)" = "your-amplify-app-id" ]; then \
		echo "Error: AMPLIFY_APP_ID is required. Set it using environment variable or update the Makefile."; \
		exit 1; \
	fi
	@if ! aws sts get-caller-identity >/dev/null 2>&1; then \
		echo "Error: AWS credentials not configured. Please run 'aws configure' or set AWS environment variables."; \
		exit 1; \
	fi
	@echo "Configuration validated"

.PHONY: setup
setup: ensure-scripts-executable validate-config
	@echo "Creating ECR repository..."
	@aws ecr describe-repositories --repository-names $(ECR_REPO_NAME) --region $(AWS_REGION) > /dev/null 2>&1 || \
		aws ecr create-repository --repository-name $(ECR_REPO_NAME) --region $(AWS_REGION)
	@echo "ECR repository '$(ECR_REPO_NAME)' created or already exists."
	@echo ""
	@echo "Note: For Amplify setup, run the following commands manually in your frontend directory:"
	@echo "  cd $(FRONTEND_DIR)"
	@echo "  amplify init"
	@echo "  amplify add hosting"
	@echo ""
	@echo "After Amplify setup, update AMPLIFY_APP_ID in this Makefile or set it as an environment variable."
	@echo "Setup complete."

.PHONY: deploy-frontend
deploy-frontend: ensure-scripts-executable validate-config
	@echo "Deploying frontend to Amplify..."
	@$(FRONTEND_DEPLOY_SCRIPT) $(AWS_REGION) $(AMPLIFY_APP_ID) $(BRANCH_NAME) $(FRONTEND_DIR)

.PHONY: build-backend
build-backend:
	@echo "Building backend Docker image..."
	@cd $(BACKEND_DIR) && \
		docker build -t $(ECR_REPO_NAME):$(BACKEND_IMAGE_TAG) .
	@echo "Backend Docker image built successfully: $(ECR_REPO_NAME):$(BACKEND_IMAGE_TAG)"

.PHONY: push-backend
push-backend: build-backend
	@echo "Pushing backend Docker image to ECR..."
	@aws ecr get-login-password --region $(AWS_REGION) | \
		docker login --username AWS --password-stdin $(shell aws sts get-caller-identity --query Account --output text).dkr.ecr.$(AWS_REGION).amazonaws.com
	@docker tag $(ECR_REPO_NAME):$(BACKEND_IMAGE_TAG) \
		$(shell aws sts get-caller-identity --query Account --output text).dkr.ecr.$(AWS_REGION).amazonaws.com/$(ECR_REPO_NAME):$(BACKEND_IMAGE_TAG)
	@docker push $(shell aws sts get-caller-identity --query Account --output text).dkr.ecr.$(AWS_REGION).amazonaws.com/$(ECR_REPO_NAME):$(BACKEND_IMAGE_TAG)
	@echo "Backend Docker image pushed to ECR successfully"

.PHONY: deploy-backend
deploy-backend: ensure-scripts-executable validate-config push-backend
	@echo "Deploying backend to App Runner..."
	@$(BACKEND_DEPLOY_SCRIPT) $(AWS_REGION) $(ECR_REPO_NAME) $(BACKEND_IMAGE_TAG) $(APP_RUNNER_SERVICE_NAME)

.PHONY: deploy
deploy: deploy-backend deploy-frontend
	@echo "Full deployment completed."
	@echo "You should now be able to access your application at:"
	@echo "- Backend: $(shell aws apprunner list-services --region $(AWS_REGION) \
		--query "ServiceSummaryList[?ServiceName=='$(APP_RUNNER_SERVICE_NAME)'].ServiceUrl" --output text)"
	@echo "- Frontend: https://$(BRANCH_NAME).$(shell aws amplify get-app --app-id $(AMPLIFY_APP_ID) --region $(AWS_REGION) \
		--query "app.defaultDomain" --output text)"

.PHONY: clean
clean:
	@echo "Cleaning up local build artifacts..."
	@cd $(FRONTEND_DIR) && rm -rf build dist node_modules/.cache
	@docker rmi -f $(ECR_REPO_NAME):$(BACKEND_IMAGE_TAG) || true
	@docker rmi -f $(shell aws sts get-caller-identity --query Account --output text).dkr.ecr.$(AWS_REGION).amazonaws.com/$(ECR_REPO_NAME):$(BACKEND_IMAGE_TAG) || true
	@echo "Clean up complete."

.PHONY: status
status: validate-config
	@echo "Checking deployment status..."
	@echo ""
	@echo "Backend (App Runner) Services:"
	@aws apprunner list-services --region $(AWS_REGION) \
		--query "ServiceSummaryList[?contains(ServiceName, '$(APP_RUNNER_SERVICE_NAME)')].{Name:ServiceName,Status:Status,URL:ServiceUrl}" \
		--output table
	@echo ""
	@echo "Frontend (Amplify) Apps:"
	@aws amplify list-apps --region $(AWS_REGION) \
		--query "apps[?contains(name, '$(AMPLIFY_APP_ID)')].{AppID:appId,Name:name,DefaultDomain:defaultDomain}" \
		--output table
	@echo ""
	@if [ -n "$(AMPLIFY_APP_ID)" ] && [ "$(AMPLIFY_APP_ID)" != "your-amplify-app-id" ]; then \
		echo "Amplify Branches for $(AMPLIFY_APP_ID):"; \
		aws amplify list-branches --app-id $(AMPLIFY_APP_ID) --region $(AWS_REGION) \
			--query "branches[].{Branch:branchName,Status:displayName,Stage:stage}" \
			--output table; \
	fi
