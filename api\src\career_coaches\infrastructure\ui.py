import streamlit as st
import requests
import json
import uuid
from streamlit_chat import message
import sys
import os

# Add parent directory to path to import from career_coaches package
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

# API URL - change this to match your deployment
API_URL = "http://localhost:8000/career-coach"
WS_URL = "ws://localhost:8000/career-coach"

# Set page config
st.set_page_config(
    page_title="Career Coach Demo",
    page_icon="👨‍💼",
    layout="wide",
)

# Initialize session state
if "user_id" not in st.session_state:
    st.session_state.user_id = str(uuid.uuid4())
if "selected_coach" not in st.session_state:
    st.session_state.selected_coach = None
if "messages" not in st.session_state:
    st.session_state.messages = []
if "web_tools" not in st.session_state:
    st.session_state.web_tools = False
if "search_tool" not in st.session_state:
    st.session_state.search_tool = "all"
if "session_goals" not in st.session_state:
    st.session_state.session_goals = []

# Function to get available coaches
@st.cache_data(ttl=300)
def get_coaches():
    try:
        response = requests.get(f"{API_URL}/coaches")
        response.raise_for_status()
        return response.json()["coaches"]
    except Exception as e:
        st.error(f"Failed to fetch coaches: {str(e)}")
        return []

# Function to reset memory
def reset_memory():
    try:
        response = requests.post(
            f"{API_URL}/reset-memory",
            json={"user_id": st.session_state.user_id}
        )
        response.raise_for_status()
        st.session_state.messages = []
        st.success("Conversation memory has been reset")
    except Exception as e:
        st.error(f"Failed to reset memory: {str(e)}")

# Function to send a message using regular HTTP endpoint
def send_message(user_input):
    # Add user message to history immediately
    st.session_state.messages.append({
        "role": "user", 
        "content": user_input
    })
    
    # Display a spinner while waiting for response
    with st.spinner("Career coach is thinking..."):
        try:
            response = requests.post(
                f"{API_URL}/chat",
                json={
                    "message": user_input,
                    "coach_id": st.session_state.selected_coach["id"],
                    "user_id": st.session_state.user_id,
                    "web_tools": st.session_state.web_tools,
                    "search_tool_name": st.session_state.search_tool,
                    "session_goals": st.session_state.session_goals,
                }
            )
            response.raise_for_status()
            
            # Extract assistant response
            assistant_response = response.json()["response"]
            
            # Add assistant response to history
            st.session_state.messages.append({
                "role": "assistant", 
                "content": assistant_response
            })
            
        except Exception as e:
            st.error(f"Error communicating with the API: {str(e)}")
            # Display detailed error for debugging
            st.error(f"Details: {repr(e)}")



# Main app UI
st.title("Career Coach Demo")

# Sidebar for settings
with st.sidebar:
    st.header("Settings")
    
    # Coach selection
    coaches = get_coaches()
    if coaches:
        # Format coaches for selection
        coach_options = {f"{coach['name']} ({coach['specialty']})": coach for coach in coaches}
        
        selected_coach_name = st.selectbox(
            "Select a career coach:",
            options=list(coach_options.keys()),
            index=0 if coach_options else None
        )
        
        if selected_coach_name:
            st.session_state.selected_coach = coach_options[selected_coach_name]
            
            # Display coach info
            st.subheader("Coach Information")
            st.write(f"**Name:** {st.session_state.selected_coach['name']}")
            st.write(f"**Specialty:** {st.session_state.selected_coach['specialty']}")
            st.write("**Focus Areas:**")
            for area in st.session_state.selected_coach['focus_areas']:
                st.write(f"- {area}")
    else:
        st.error("No coaches available. Make sure the Career Coach API is running.")
    
    # Web tools toggle
    st.session_state.web_tools = st.checkbox("Enable Web Tools", value=st.session_state.web_tools)
    
    # Search tool selection
    if st.session_state.web_tools:
        st.session_state.search_tool = st.radio(
            "Search Tool:",
            options=["all", "tavily", "serper", "ddg"],
            index=0
        )
    
    # Session goals
    st.subheader("Session Goals")
    new_goal = st.text_input("Add a session goal:")
    if new_goal:
        if new_goal not in st.session_state.session_goals:
            st.session_state.session_goals.append(new_goal)
    
    # Display and manage goals
    for i, goal in enumerate(st.session_state.session_goals):
        cols = st.columns([0.8, 0.2])
        cols[0].write(f"{i+1}. {goal}")
        if cols[1].button("🗑️", key=f"delete_{i}"):
            st.session_state.session_goals.pop(i)
            st.rerun()
    
    # Reset conversation button
    if st.button("Reset Conversation"):
        reset_memory()

# Main chat interface
if st.session_state.selected_coach:
    # Display chat messages from history
    for i, msg in enumerate(st.session_state.messages):
        message(
            msg["content"],
            is_user=msg["role"] == "user",
            key=f"{msg['role']}_{i}"
        )
    
    # Chat input
    user_input = st.chat_input("Type your message here...")
    if user_input:
        send_message(user_input)
        # Force a rerun to update the UI
        st.rerun()
else:
    st.info("Please select a career coach from the sidebar to begin.")

# Footer
st.markdown("---")
st.markdown("Career Coach Demo | Powered by FastAPI and Streamlit")

# Run the app with: streamlit run ui.py
