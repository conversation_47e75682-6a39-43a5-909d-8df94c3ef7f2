from contextlib import asynccontextmanager
from datetime import datetime
import time

from fastapi import FastAP<PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from opik.integrations.langchain import OpikTracer
from pydantic import BaseModel
from loguru import logger

from career_coaches.infrastructure.history_api import router as history_router
from career_coaches.infrastructure.supabase_history_api import router as supabase_history_router

from career_coaches.application.conversation_service.generate_response import (
    get_response,
    get_streaming_response,
)
from career_coaches.application.conversation_service.reset_conversation import (
    reset_conversation_state,
)
from career_coaches.application.progress_tracker import UserProgressTracker, ProgressStatus
from career_coaches.application.monitoring import monitor
from career_coaches.config import settings
from career_coaches.domain.coach_factory import CoachFactory
from common.infrastructure.opik_utils import configure

configure(settings.COMET_API_KEY, settings.CAREER_COACH_PROJECT)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handles startup and shutdown events for the Career Coach API."""
    # Startup code (if any) goes here
    yield
    # Shutdown code goes here
    opik_tracer = OpikTracer()
    opik_tracer.flush()


app = FastAPI(
    title="Career Coach API",
    description="API for career coaching agents with multi-user support",
    version="1.0.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# # Include the history API router
# app.include_router(history_router, prefix="/history", tags=["history"])

# Include the Supabase history API router
app.include_router(supabase_history_router, prefix="/supabase-history", tags=["supabase-history"])


class ChatMessage(BaseModel):
    message: str
    coach_id: str
    user_id: str
    session_id: str = ""  # Optional session ID for tracking
    user_context: str = ""
    session_goals: list[str] = []
    web_tools: bool = False
    search_tool_name: str = "all"  # Options: tavily, serper, ddg, all


class CreateSessionRequest(BaseModel):
    coach_id: str
    user_id: str
    title: str = None


class ResetMemoryRequest(BaseModel):
    user_id: str = None  # Optional: if provided, reset only for this user


class AddGoalRequest(BaseModel):
    user_id: str
    coach_id: str
    title: str
    description: str
    target_date: str
    category: str
    priority: int = 3


class UpdateGoalProgressRequest(BaseModel):
    user_id: str
    coach_id: str
    goal_id: str
    status: str = None
    completion_percentage: int = None
    milestone_title: str = None
    milestone_description: str = None


class AddAchievementRequest(BaseModel):
    user_id: str
    coach_id: str
    title: str
    description: str
    category: str
    impact_level: int = 3
    evidence: str = ""
    skills_gained: list[str] = []


class UpdateSkillRequest(BaseModel):
    user_id: str
    coach_id: str
    skill_name: str
    current_level: int
    target_level: int = None
    learning_resources: list[str] = []


@app.post("/chat")
async def chat(chat_message: ChatMessage):
    """Chat endpoint for career coaching conversations."""
    try:
        coach_factory = CoachFactory()
        coach = coach_factory.get_coach(chat_message.coach_id)

        response, _ = await get_response(
            messages=chat_message.message,
            user_id=chat_message.user_id,
            coach_id=chat_message.coach_id,
            coach_name=coach.name,
            coach_specialty=coach.specialty,
            coach_approach=coach.approach,
            coach_focus_areas=coach.focus_areas,
            user_context=chat_message.user_context,
            session_goals=chat_message.session_goals,
            use_web_tools=chat_message.web_tools,
            search_tool_name=chat_message.search_tool_name,
        )
        return {"response": response}
    except Exception as e:
        opik_tracer = OpikTracer()
        opik_tracer.flush()

        raise HTTPException(status_code=500, detail=str(e))


@app.websocket("/ws/chat")
async def websocket_chat(websocket: WebSocket):
    """WebSocket endpoint for streaming career coaching conversations with session support."""
    await websocket.accept()

    try:
        while True:
            data = await websocket.receive_json()

            required_fields = ["message", "coach_id", "user_id"]
            if not all(field in data for field in required_fields):
                await websocket.send_json(
                    {
                        "error": f"Invalid message format. Required fields: {required_fields}"
                    }
                )
                continue

            try:
                from career_coaches.application.history_service import ChatHistoryService

                coach_factory = CoachFactory()
                coach = coach_factory.get_coach(data["coach_id"])

                session_id = data.get("session_id", "")
                user_id = data["user_id"]
                coach_id = data["coach_id"]

                # Load existing session messages if session_id is provided
                existing_messages = []
                if session_id:
                    try:
                        session_data = await ChatHistoryService.get_chat_session(user_id, session_id)
                        if session_data and session_data.get("messages"):
                            import json
                            existing_messages = json.loads(session_data["messages"])
                    except Exception as e:
                        logger.warning(f"Could not load session {session_id}: {e}")

                # Use streaming response
                response_stream = get_streaming_response(
                    messages=data["message"],
                    user_id=user_id,
                    coach_id=coach_id,
                    coach_name=coach.name,
                    coach_specialty=coach.specialty,
                    coach_approach=coach.approach,
                    coach_focus_areas=coach.focus_areas,
                    user_context=data.get("user_context", ""),
                    session_goals=data.get("session_goals", []),
                    use_web_tools=data.get("web_tools", False),
                    search_tool_name=data.get("search_tool_name", "all"),
                )

                # Send initial message to indicate streaming has started
                await websocket.send_json({"streaming": True, "session_id": session_id})

                # Stream each chunk of the response
                full_response = ""
                async for chunk in response_stream:
                    full_response += chunk
                    await websocket.send_json({"chunk": chunk})

                # Save the conversation to session if session_id is provided
                if session_id and full_response:
                    try:
                        # Add new messages to existing ones
                        new_messages = existing_messages + [
                            {
                                "id": f"user_{int(time.time() * 1000)}",
                                "role": "user",
                                "content": data["message"],
                                "timestamp": datetime.utcnow().isoformat()
                            },
                            {
                                "id": f"assistant_{int(time.time() * 1000) + 1}",
                                "role": "assistant",
                                "content": full_response,
                                "timestamp": datetime.utcnow().isoformat()
                            }
                        ]

                        await ChatHistoryService.save_chat_session(
                            user_id=user_id,
                            coach_id=coach_id,
                            chat_session_id=session_id,
                            messages=new_messages
                        )
                    except Exception as e:
                        logger.error(f"Failed to save session: {e}")

                await websocket.send_json(
                    {"response": full_response, "streaming": False, "session_id": session_id}
                )

            except Exception as e:
                opik_tracer = OpikTracer()
                opik_tracer.flush()

                await websocket.send_json({"error": str(e)})

    except WebSocketDisconnect:
        pass


@app.post("/reset-memory")
async def reset_memory(request: ResetMemoryRequest):
    """Resets the conversation state for career coaches.
    
    Can reset for all users or a specific user.

    Raises:
        HTTPException: If there is an error resetting the conversation state.
    Returns:
        dict: A dictionary containing the result of the reset operation.
    """
    try:
        result = await reset_conversation_state(user_id=request.user_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/coaches")
async def get_available_coaches():
    """Get list of available career coaches."""
    try:
        coach_factory = CoachFactory()
        available_coaches = coach_factory.get_available_coaches()

        coaches_info = []
        for coach_id in available_coaches:
            coach = coach_factory.get_coach(coach_id)
            coaches_info.append({
                "id": coach.id,
                "name": coach.name,
                "specialty": coach.specialty,
                "focus_areas": coach.focus_areas
            })

        return {"coaches": coaches_info}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/create-session")
async def create_chat_session(request: CreateSessionRequest):
    """Create a new chat session for a coach."""
    try:
        import uuid
        from career_coaches.application.history_service import ChatHistoryService

        chat_session_id = str(uuid.uuid4())

        session = await ChatHistoryService.save_chat_session(
            user_id=request.user_id,
            coach_id=request.coach_id,
            chat_session_id=chat_session_id,
            messages=[],
            title=request.title or f"New Chat Session"
        )

        return {
            "success": True,
            "session": session,
            "chat_session_id": chat_session_id,
            "redirect_url": f"/app/careerCoach/{request.coach_id}/{chat_session_id}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create chat session: {str(e)}")


@app.post("/progress/add-goal")
async def add_goal(request: AddGoalRequest):
    """Add a new goal for the user."""
    try:
        progress_tracker = UserProgressTracker()
        goal = progress_tracker.add_goal(
            user_id=request.user_id,
            coach_id=request.coach_id,
            title=request.title,
            description=request.description,
            target_date=request.target_date,
            category=request.category,
            priority=request.priority
        )
        return {"success": True, "goal": goal.__dict__}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/progress/update-goal")
async def update_goal_progress(request: UpdateGoalProgressRequest):
    """Update progress on a specific goal."""
    try:
        progress_tracker = UserProgressTracker()

        status = None
        if request.status:
            status = ProgressStatus(request.status)

        milestone = None
        if request.milestone_title:
            milestone = {
                "title": request.milestone_title,
                "description": request.milestone_description or "",
                "date": datetime.utcnow().isoformat()
            }

        progress_tracker.update_goal_progress(
            user_id=request.user_id,
            coach_id=request.coach_id,
            goal_id=request.goal_id,
            status=status,
            completion_percentage=request.completion_percentage,
            milestone=milestone
        )
        return {"success": True, "message": "Goal progress updated"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/progress/add-achievement")
async def add_achievement(request: AddAchievementRequest):
    """Record a new achievement for the user."""
    try:
        progress_tracker = UserProgressTracker()
        achievement = progress_tracker.add_achievement(
            user_id=request.user_id,
            coach_id=request.coach_id,
            title=request.title,
            description=request.description,
            category=request.category,
            impact_level=request.impact_level,
            evidence=request.evidence,
            skills_gained=request.skills_gained
        )
        return {"success": True, "achievement": achievement.__dict__}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/progress/update-skill")
async def update_skill(request: UpdateSkillRequest):
    """Update or create a skill assessment."""
    try:
        progress_tracker = UserProgressTracker()
        skill_assessment = progress_tracker.update_skill_assessment(
            user_id=request.user_id,
            coach_id=request.coach_id,
            skill_name=request.skill_name,
            current_level=request.current_level,
            target_level=request.target_level,
            learning_resources=request.learning_resources
        )
        return {"success": True, "skill_assessment": skill_assessment.__dict__}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/progress/summary/{user_id}")
async def get_progress_summary(user_id: str, coach_id: str = None):
    """Get a comprehensive progress summary for the user."""
    try:
        progress_tracker = UserProgressTracker()
        summary = progress_tracker.get_user_progress_summary(user_id, coach_id)
        return summary
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/monitoring/coach-performance/{coach_id}")
async def get_coach_performance(coach_id: str, days: int = 7):
    """Get performance metrics for a specific coach."""
    try:
        performance_metrics = monitor.get_coach_performance_metrics(coach_id, days)
        if performance_metrics:
            return performance_metrics.__dict__
        else:
            return {"error": "Could not retrieve performance metrics"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/monitoring/active-sessions")
async def get_active_sessions():
    """Get information about currently active coaching sessions."""
    try:
        active_sessions = {}
        for session_id, session_data in monitor.active_sessions.items():
            active_sessions[session_id] = {
                "user_id": session_data["user_id"],
                "coach_id": session_data["coach_id"],
                "start_time": session_data["start_time"],
                "message_count": session_data["message_count"],
                "duration_minutes": (time.time() - datetime.fromisoformat(session_data["start_time"]).timestamp()) / 60
            }
        return {"active_sessions": active_sessions, "total_active": len(active_sessions)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/monitoring/rate-session")
async def rate_session(session_id: str, user_satisfaction: float = None, coaching_effectiveness: float = None):
    """Rate a coaching session for monitoring purposes."""
    try:
        # This would typically be called after a session ends
        # For now, we'll just log the ratings
        logger.info(f"Session {session_id} rated - Satisfaction: {user_satisfaction}, Effectiveness: {coaching_effectiveness}")
        return {"success": True, "message": "Session rating recorded"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "career_coaches"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8001)
