{"cells": [{"cell_type": "code", "execution_count": null, "id": "2563f077", "metadata": {}, "outputs": [], "source": ["# Career Coach Reflexi<PERSON> Testing Notebook\n", "# This notebook tests the improved career coach with self-reflection capabilities\n", "\n", "import asyncio\n", "import sys\n", "import os\n", "from typing import Dict, List, Any, Tuple\n", "from dataclasses import dataclass\n", "from enum import Enum\n", "import json\n", "import time\n", "sys.path.insert(0, os.path.abspath('../src'))\n", "# Import existing career coach components\n", "from career_coaches.domain.coach_factory import CoachFactory\n", "from career_coaches.domain.coach import Coach\n", "from career_coaches.application.conversation_service.workflow.state import CareerCoachState\n", "from career_coaches.application.conversation_service.workflow.chains import get_career_coach_response_chain\n", "from langchain_core.messages import HumanMessage, AIMessage\n", "\n", "# ============================================================================\n", "# REFLEXION PATTERN IMPLEMENTATION\n", "# ============================================================================\n", "\n", "class QueryType(Enum):\n", "    SIMPLE_QUESTION = \"simple_question\"\n", "    ADVICE_REQUEST = \"advice_request\"\n", "    COMPLEX_ANALYSIS = \"complex_analysis\"\n", "    EMERGENCY_HELP = \"emergency_help\"\n", "\n", "@dataclass\n", "class ReflexionScore:\n", "    brevity: float       # 0-10: Is response appropriately concise?\n", "    relevance: float     # 0-10: Does it address user's specific need?\n", "    actionability: float # 0-10: Can user immediately act on advice?\n", "    clarity: float       # 0-10: Is the response clear and understandable?\n", "    overall: float = 0.0       # Weighted average\n", "    \n", "    def __post_init__(self):\n", "        weights = {\"brevity\": 0.25, \"relevance\": 0.30, \"actionability\": 0.25, \"clarity\": 0.20}\n", "        self.overall = (\n", "            self.brevity * weights[\"brevity\"] +\n", "            self.relevance * weights[\"relevance\"] +\n", "            self.actionability * weights[\"actionability\"] +\n", "            self.clarity * weights[\"clarity\"]\n", "        )\n", "\n", "class ResponseOptimizer:\n", "    def __init__(self):\n", "        # Target word counts for different query types\n", "        self.target_lengths = {\n", "            QueryType.SIMPLE_QUESTION: 50,      # 2-3 sentences\n", "            QueryType.ADVICE_REQUEST: 150,     # 5-7 sentences  \n", "            QueryType.COMPLEX_ANALYSIS: 300,   # Detailed response\n", "            QueryType.EMERGENCY_HELP: 75       # Quick, actionable advice\n", "        }\n", "    \n", "    def classify_query(self, query: str) -> QueryType:\n", "        \"\"\"Classify the type of query to determine appropriate response length.\"\"\"\n", "        query_lower = query.lower()\n", "        \n", "        # Simple question indicators\n", "        if any(word in query_lower for word in [\"what is\", \"define\", \"explain briefly\", \"quick question\"]):\n", "            return QueryType.SIMPLE_QUESTION\n", "        \n", "        # Emergency help indicators\n", "        if any(word in query_lower for word in [\"urgent\", \"emergency\", \"asap\", \"immediately\", \"quick help\"]):\n", "            return QueryType.EMERGENCY_HELP\n", "        \n", "        # Complex analysis indicators\n", "        if any(word in query_lower for word in [\"analyze\", \"detailed\", \"comprehensive\", \"strategy\", \"plan\"]):\n", "            return QueryType.COMPLEX_ANALYSIS\n", "        \n", "        # Default to advice request\n", "        return QueryType.ADVICE_REQUEST\n", "    \n", "    def evaluate_verbosity(self, response: str, query_type: QueryType) -> float:\n", "        \"\"\"Evaluate if response length is appropriate. Returns score 0-10.\"\"\"\n", "        target = self.target_lengths[query_type]\n", "        actual_words = len(response.split())\n", "        \n", "        # Calculate deviation from target\n", "        deviation = abs(actual_words - target) / target\n", "        \n", "        # Score based on deviation (less deviation = higher score)\n", "        if deviation <= 0.2:  # Within 20% of target\n", "            return 10.0\n", "        elif deviation <= 0.4:  # Within 40% of target\n", "            return 8.0\n", "        elif deviation <= 0.6:  # Within 60% of target\n", "            return 6.0\n", "        elif deviation <= 0.8:  # Within 80% of target\n", "            return 4.0\n", "        else:\n", "            return 2.0\n", "\n", "class ReflexionEvaluator:\n", "    def __init__(self):\n", "        self.optimizer = ResponseOptimizer()\n", "    \n", "    def evaluate_response(self, query: str, response: str, coach_specialty: str) -> ReflexionScore:\n", "        \"\"\"Evaluate response quality across multiple dimensions.\"\"\"\n", "        \n", "        query_type = self.optimizer.classify_query(query)\n", "        \n", "        # Evaluate brevity\n", "        brevity_score = self.optimizer.evaluate_verbosity(response, query_type)\n", "        \n", "        # Evaluate relevance (simple heuristic - can be improved with ML)\n", "        relevance_score = self._evaluate_relevance(query, response, coach_specialty)\n", "        \n", "        # Evaluate actionability\n", "        actionability_score = self._evaluate_actionability(response)\n", "        \n", "        # Evaluate clarity\n", "        clarity_score = self._evaluate_clarity(response)\n", "        \n", "        return ReflexionScore(\n", "            brevity=brevity_score,\n", "            relevance=relevance_score,\n", "            actionability=actionability_score,\n", "            clarity=clarity_score\n", "        )\n", "    \n", "    def _evaluate_relevance(self, query: str, response: str, coach_specialty: str) -> float:\n", "        \"\"\"Evaluate if response is relevant to the query and coach specialty.\"\"\"\n", "        query_words = set(query.lower().split())\n", "        response_words = set(response.lower().split())\n", "        specialty_words = set(coach_specialty.lower().split())\n", "        \n", "        # Calculate word overlap\n", "        query_overlap = len(query_words.intersection(response_words)) / len(query_words)\n", "        specialty_relevance = len(specialty_words.intersection(response_words)) / len(specialty_words)\n", "        \n", "        # Combined score\n", "        relevance = (query_overlap * 0.7 + specialty_relevance * 0.3) * 10\n", "        return min(relevance, 10.0)\n", "    \n", "    def _evaluate_actionability(self, response: str) -> float:\n", "        \"\"\"Evaluate if response contains actionable advice.\"\"\"\n", "        action_indicators = [\n", "            \"should\", \"can\", \"try\", \"consider\", \"start\", \"begin\", \"create\", \"update\",\n", "            \"apply\", \"reach out\", \"contact\", \"schedule\", \"plan\", \"prepare\", \"research\"\n", "        ]\n", "        \n", "        response_lower = response.lower()\n", "        action_count = sum(1 for indicator in action_indicators if indicator in response_lower)\n", "        \n", "        # Score based on presence of action words\n", "        if action_count >= 3:\n", "            return 10.0\n", "        elif action_count >= 2:\n", "            return 8.0\n", "        elif action_count >= 1:\n", "            return 6.0\n", "        else:\n", "            return 3.0\n", "    \n", "    def _evaluate_clarity(self, response: str) -> float:\n", "        \"\"\"Evaluate response clarity based on sentence structure.\"\"\"\n", "        sentences = response.split('.')\n", "        avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)\n", "        \n", "        # Optimal sentence length is 15-20 words\n", "        if 10 <= avg_sentence_length <= 25:\n", "            return 10.0\n", "        elif 8 <= avg_sentence_length <= 30:\n", "            return 8.0\n", "        elif 5 <= avg_sentence_length <= 35:\n", "            return 6.0\n", "        else:\n", "            return 4.0\n", "\n", "class ResponseRefiner:\n", "    def __init__(self):\n", "        self.evaluator = ReflexionEvaluator()\n", "    \n", "    def refine_response(self, query: str, original_response: str, coach_specialty: str, \n", "                      reflection_score: ReflexionScore) -> str:\n", "        \"\"\"Refine response based on reflection score.\"\"\"\n", "        \n", "        if reflection_score.overall >= 7.0:\n", "            return original_response  # Response is good enough\n", "        \n", "        refined_response = original_response\n", "        \n", "        # Address brevity issues\n", "        if reflection_score.brevity < 6.0:\n", "            refined_response = self._condense_response(refined_response)\n", "        \n", "        # Address actionability issues\n", "        if reflection_score.actionability < 6.0:\n", "            refined_response = self._add_actionable_advice(refined_response)\n", "        \n", "        # Address clarity issues\n", "        if reflection_score.clarity < 6.0:\n", "            refined_response = self._improve_clarity(refined_response)\n", "        \n", "        return refined_response\n", "    \n", "    def _condense_response(self, response: str) -> str:\n", "        \"\"\"Condense verbose responses.\"\"\"\n", "        sentences = [s.strip() for s in response.split('.') if s.strip()]\n", "        \n", "        # Keep most important sentences (first and last, remove middle filler)\n", "        if len(sentences) > 4:\n", "            condensed = sentences[:2] + sentences[-2:]\n", "            return '. '.join(condensed) + '.'\n", "        \n", "        return response\n", "    \n", "    def _add_actionable_advice(self, response: str) -> str:\n", "        \"\"\"Add actionable elements to response.\"\"\"\n", "        if not any(word in response.lower() for word in [\"should\", \"can\", \"try\", \"consider\"]):\n", "            response += \" Consider taking these specific steps to move forward.\"\n", "        return response\n", "    \n", "    def _improve_clarity(self, response: str) -> str:\n", "        \"\"\"Improve response clarity.\"\"\"\n", "        # Split long sentences\n", "        sentences = response.split('.')\n", "        improved_sentences = []\n", "        \n", "        for sentence in sentences:\n", "            if len(sentence.split()) > 25:  # Long sentence\n", "                # Simple splitting on conjunctions\n", "                if ' and ' in sentence:\n", "                    parts = sentence.split(' and ')\n", "                    improved_sentences.extend(parts)\n", "                else:\n", "                    improved_sentences.append(sentence)\n", "            else:\n", "                improved_sentences.append(sentence)\n", "        \n", "        return '. '.join(s.strip() for s in improved_sentences if s.strip()) + '.'\n", "\n", "# ============================================================================\n", "# REFLEXION CAREER COACH IMPLEMENTATION\n", "# ============================================================================\n", "\n", "class ReflexionCareerCoach:\n", "    def __init__(self, coach_id: str = \"career_assessment\"):\n", "        self.coach_factory = CoachFactory()\n", "        self.coach = self.coach_factory.get_coach(coach_id)\n", "        self.evaluator = ReflexionEvaluator()\n", "        self.refiner = ResponseRefiner()\n", "        self.conversation_history = []\n", "        \n", "        print(f\"🤖 Initialized Reflexion Career Coach: {self.coach.name}\")\n", "        print(f\"📋 Specialty: {self.coach.specialty}\")\n", "        print(f\"🎯 Focus Areas: {', '.join(self.coach.focus_areas)}\")\n", "    \n", "    async def process_query_with_reflexion(self, query: str, max_iterations: int = 3) -> Dict[str, Any]:\n", "        \"\"\"Process query with reflexion loop.\"\"\"\n", "        \n", "        print(f\"\\n🔄 Processing query with reflexion pattern...\")\n", "        print(f\"📝 Query: {query}\")\n", "        \n", "        # Step 1: Planning\n", "        query_type = self.evaluator.optimizer.classify_query(query)\n", "        print(f\"🎯 Query Type: {query_type.value}\")\n", "        \n", "        # Step 2: Initial Response Generation\n", "        initial_response = await self._generate_response(query)\n", "        print(f\"💭 Initial Response ({len(initial_response.split())} words): {initial_response}\")\n", "        \n", "        # Step 3: Reflexion Loop\n", "        current_response = initial_response\n", "        iteration = 0\n", "        reflexion_history = []\n", "        \n", "        while iteration < max_iterations:\n", "            iteration += 1\n", "            print(f\"\\n🔍 Reflexion Iteration {iteration}\")\n", "            \n", "            # Evaluate current response\n", "            reflection_score = self.evaluator.evaluate_response(\n", "                query, current_response, self.coach.specialty\n", "            )\n", "            \n", "            print(f\"📊 Reflection Scores:\")\n", "            print(f\"   Brevity: {reflection_score.brevity:.1f}/10\")\n", "            print(f\"   Relevance: {reflection_score.relevance:.1f}/10\")\n", "            print(f\"   Actionability: {reflection_score.actionability:.1f}/10\")\n", "            print(f\"   Clarity: {reflection_score.clarity:.1f}/10\")\n", "            print(f\"   Overall: {reflection_score.overall:.1f}/10\")\n", "            \n", "            reflexion_history.append({\n", "                \"iteration\": iteration,\n", "                \"response\": current_response,\n", "                \"score\": reflection_score\n", "            })\n", "            \n", "            # Check if response is good enough\n", "            if reflection_score.overall >= 7.0:\n", "                print(f\"✅ Response quality acceptable (score: {reflection_score.overall:.1f})\")\n", "                break\n", "            \n", "            # Refine response\n", "            print(f\"🔧 Refining response...\")\n", "            current_response = self.refiner.refine_response(\n", "                query, current_response, self.coach.specialty, reflection_score\n", "            )\n", "            print(f\"💭 Refined Response ({len(current_response.split())} words): {current_response}\")\n", "        \n", "        # Store conversation\n", "        self.conversation_history.append({\n", "            \"query\": query,\n", "            \"final_response\": current_response,\n", "            \"reflexion_history\": reflexion_history,\n", "            \"timestamp\": time.time()\n", "        })\n", "        \n", "        return {\n", "            \"query\": query,\n", "            \"final_response\": current_response,\n", "            \"iterations\": iteration,\n", "            \"final_score\": reflexion_history[-1][\"score\"] if reflexion_history else None,\n", "            \"improvement\": reflexion_history[-1][\"score\"].overall - reflexion_history[0][\"score\"].overall if len(reflexion_history) > 1 else 0\n", "        }\n", "    \n", "    async def _generate_response(self, query: str) -> str:\n", "        \"\"\"Generate initial response using existing coach chain.\"\"\"\n", "        try:\n", "            # Create a simple state for testing\n", "            messages = [HumanMessage(content=query)]\n", "            \n", "            # Get conversation chain\n", "            conversation_chain = get_career_coach_response_chain(self.coach.id, use_web_tools=False)\n", "            \n", "            # Generate response\n", "            response = await conversation_chain.ainvoke({\n", "                \"messages\": messages,\n", "                \"summary\": \"\"\n", "            })\n", "            \n", "            # Extract content from AIMessage\n", "            if hasattr(response, 'content'):\n", "                return response.content\n", "            elif isinstance(response, list) and len(response) > 0:\n", "                return response[-1].content if hasattr(response[-1], 'content') else str(response[-1])\n", "            else:\n", "                return str(response)\n", "                \n", "        except Exception as e:\n", "            print(f\"⚠️ Error generating response: {e}\")\n", "            return f\"I understand you're asking about {query}. Let me provide some helpful guidance on this topic.\"\n", "    \n", "    def get_conversation_summary(self) -> Dict[str, Any]:\n", "        \"\"\"Get summary of conversation history and improvements.\"\"\"\n", "        if not self.conversation_history:\n", "            return {\"message\": \"No conversations yet\"}\n", "        \n", "        total_conversations = len(self.conversation_history)\n", "        total_improvements = sum(conv.get(\"improvement\", 0) for conv in self.conversation_history)\n", "        avg_improvement = total_improvements / total_conversations\n", "        \n", "        return {\n", "            \"total_conversations\": total_conversations,\n", "            \"average_improvement\": avg_improvement,\n", "            \"recent_conversations\": self.conversation_history[-3:] if len(self.conversation_history) >= 3 else self.conversation_history\n", "        }\n", "\n", "# ============================================================================\n", "# TESTING FRAMEWORK\n", "# ============================================================================\n", "\n", "class ReflexionTester:\n", "    def __init__(self):\n", "        self.test_queries = [\n", "            {\n", "                \"query\": \"What is a resume?\",\n", "                \"expected_type\": QueryType.SIMPLE_QUESTION,\n", "                \"description\": \"Simple definition question\"\n", "            },\n", "            {\n", "                \"query\": \"I need urgent help with my job interview tomorrow. What should I do?\",\n", "                \"expected_type\": QueryType.EMERGENCY_HELP,\n", "                \"description\": \"Emergency help request\"\n", "            },\n", "            {\n", "                \"query\": \"Can you help me create a comprehensive career development strategy for transitioning from marketing to data science?\",\n", "                \"expected_type\": QueryType.COMPLEX_ANALYSIS,\n", "                \"description\": \"Complex career strategy request\"\n", "            },\n", "            {\n", "                \"query\": \"How can I improve my LinkedIn profile to get more recruiter attention?\",\n", "                \"expected_type\": QueryType.ADVICE_REQUEST,\n", "                \"description\": \"Standard advice request\"\n", "            },\n", "            {\n", "                \"query\": \"I've been unemployed for 6 months and feeling discouraged. I have a marketing background but interested in tech. I've been applying to jobs but not getting responses. My resume might need work and I'm not sure about my LinkedIn profile. I also don't know if I should consider a career change or stick with marketing. What should I do? I'm feeling overwhelmed and don't know where to start. Please help me with everything.\",\n", "                \"expected_type\": QueryType.ADVICE_REQUEST,\n", "                \"description\": \"Verbose, overwhelming query (test verbosity control)\"\n", "            }\n", "        ]\n", "    \n", "    async def run_comprehensive_test(self):\n", "        \"\"\"Run comprehensive test of reflexion pattern.\"\"\"\n", "        print(\"🧪 Starting Comprehensive Reflexion Pattern Test\")\n", "        print(\"=\" * 60)\n", "        \n", "        # Test different coach types\n", "        coach_types = [\"career_assessment\", \"resume_builder\", \"linkedin_optimizer\", \"networking_strategy\"]\n", "        \n", "        for coach_id in coach_types:\n", "            print(f\"\\n🎭 Testing Coach: {coach_id}\")\n", "            print(\"-\" * 40)\n", "            \n", "            coach = ReflexionCareerCoach(coach_id)\n", "            \n", "            # Test a few queries for each coach\n", "            test_queries = self.test_queries[:3]  # Use first 3 queries for each coach\n", "            \n", "            for i, test_case in enumerate(test_queries):\n", "                print(f\"\\n📋 Test Case {i+1}: {test_case['description']}\")\n", "                \n", "                result = await coach.process_query_with_reflexion(test_case[\"query\"])\n", "                \n", "                print(f\"📈 Results:\")\n", "                print(f\"   Iterations: {result['iterations']}\")\n", "                print(f\"   Final Score: {result['final_score'].overall:.1f}/10\")\n", "                print(f\"   Improvement: +{result['improvement']:.1f}\")\n", "                print(f\"   Final Response: {result['final_response'][:150]}...\")\n", "        \n", "        print(\"\\n🎉 Comprehensive test completed!\")\n", "    \n", "    async def run_quick_test(self):\n", "        \"\"\"Run quick test with one coach and one query.\"\"\"\n", "        print(\"⚡ Quick Reflexion Pattern Test\")\n", "        print(\"=\" * 40)\n", "        \n", "        coach = ReflexionCareerCoach(\"career_assessment\")\n", "        \n", "        # Test the verbose query\n", "        verbose_query = self.test_queries[4][\"query\"]\n", "        \n", "        result = await coach.process_query_with_reflexion(verbose_query)\n", "        \n", "        print(f\"\\n📊 Final Results:\")\n", "        print(f\"Original query length: {len(verbose_query.split())} words\")\n", "        print(f\"Final response length: {len(result['final_response'].split())} words\")\n", "        print(f\"Quality improvement: +{result['improvement']:.1f} points\")\n", "        print(f\"Iterations needed: {result['iterations']}\")\n", "        \n", "        print(f\"\\n📝 Final Response:\")\n", "        print(f\"{result['final_response']}\")\n", "        \n", "        return result\n"]}, {"cell_type": "code", "execution_count": 20, "id": "50cbfc6d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Initializing Reflexion Career Coach Test\n", "This notebook tests the new reflexion pattern without database changes\n", "======================================================================\n", "⚡ Quick Reflexion Pattern Test\n", "========================================\n", "🤖 Initialized Reflexion Career Coach: Career Assessment Coach\n", "📋 Specialty: Career Assessment & Path Planning\n", "🎯 Focus Areas: MBTI personality assessment and career alignment, Strong Interest Inventory for career matching, PRINT assessment for unconscious motivators and stress triggers, Holland Code (RIASEC) test interpretation, Risk propensity evaluation for industry fit, Self-exploration using Columbia University tools, Career path recommendations based on personality and interests, InternUp Industrial Projects matching and recommendations\n", "\n", "🔄 Processing query with reflexion pattern...\n", "📝 Query: I've been unemployed for 6 months and feeling discouraged. I have a marketing background but interested in tech. I've been applying to jobs but not getting responses. My resume might need work and I'm not sure about my LinkedIn profile. I also don't know if I should consider a career change or stick with marketing. What should I do? I'm feeling overwhelmed and don't know where to start. Please help me with everything.\n", "🎯 Query Type: advice_request\n", "💭 Initial Response (143 words): I'm so glad you reached out for help, and I'm more than happy to support you in this journey. First,...\n", "\n", "🔍 Reflexion Iteration 1\n", "📊 Reflection Scores:\n", "   Brevity: 10.0/10\n", "   Relevance: 3.3/10\n", "   Actionability: 10.0/10\n", "   Clarity: 10.0/10\n", "   Overall: 8.0/10\n", "✅ Response quality acceptable (score: 8.0)\n", "\n", "📊 Final Results:\n", "Original query length: 73 words\n", "Final response length: 143 words\n", "Quality improvement: +0.0 points\n", "Iterations needed: 1\n", "\n", "📝 Final Response:\n", "I'm so glad you reached out for help, and I'm more than happy to support you in this journey. First, let me just say that it's completely normal to feel overwhelmed and uncertain about your career path, especially after being unemployed for a while. You're not alone in this, and I'm here to help you break it down into smaller, manageable steps.\n", "\n", "Before we dive into the nitty-gritty, I want to acknowledge that you have a marketing background, but you're interested in exploring tech. That's a great starting point! It takes a lot of courage to consider a potential career shift, and I'm excited to help you explore this further.\n", "\n", "To get started, let's take a deep breath and focus on one thing at a time. I'd love to get to know you a bit better. Can you tell me, what's your name?\n", "\n", "======================================================================\n", "✅ Quick test completed! You can now run the comprehensive test or test individual queries.\n", "\n", "💡 Next steps:\n", "1. Run `await tester.run_comprehensive_test()` for full testing\n", "2. Create your own ReflexionCareerCoach instance and test custom queries\n", "3. Analyze the reflexion_history in results to see improvement process\n"]}], "source": ["\n", "# ============================================================================\n", "# NOTEBOOK EXECUTION\n", "# ============================================================================\n", "\n", "# Run the quick test\n", "print(\"🚀 Initializing Reflexion Career Coach Test\")\n", "print(\"This notebook tests the new reflexion pattern without database changes\")\n", "print(\"=\" * 70)\n", "\n", "# Initialize and run quick test\n", "tester = ReflexionTester()\n", "quick_result = await tester.run_quick_test()\n", "\n", "print(\"\\n\" + \"=\" * 70)\n", "print(\"✅ Quick test completed! You can now run the comprehensive test or test individual queries.\")\n", "print(\"\\n💡 Next steps:\")\n", "print(\"1. Run `await tester.run_comprehensive_test()` for full testing\")\n", "print(\"2. Create your own ReflexionCareerCoach instance and test custom queries\")\n", "print(\"3. Analyze the reflexion_history in results to see improvement process\")"]}, {"cell_type": "code", "execution_count": 21, "id": "49b36cf5", "metadata": {}, "outputs": [], "source": ["import uuid\n", "import random\n", "from datetime import datetime\n", "from typing import Optional\n", "\n", "class ChatSession:\n", "    \"\"\"Simple chat session manager\"\"\"\n", "    \n", "    def __init__(self, coach_id: str = \"career_assessment\"):\n", "        self.session_id = str(uuid.uuid4())[:8]  # Short session ID\n", "        self.user_id = self._generate_random_user()\n", "        self.conversation_history = []\n", "        self.created_at = datetime.now()\n", "        self.is_active = True\n", "        \n", "        # Initialize the reflexion career coach directly\n", "        self.reflexion_coach = ReflexionCareerCoach(coach_id)\n", "        \n", "    def _generate_random_user(self) -> str:\n", "        \"\"\"Generate a random user ID\"\"\"\n", "        prefixes = [\"user\", \"guest\", \"visitor\", \"student\", \"professional\"]\n", "        suffix = random.randint(1000, 9999)\n", "        return f\"{random.choice(prefixes)}_{suffix}\"\n", "    \n", "    async def send_message(self, message: str) -> str:\n", "        \"\"\"Send a message and get response from the reflexion agent\"\"\"\n", "        if not self.is_active:\n", "            return \"❌ Session is inactive. Please start a new session.\"\n", "        \n", "        # Add user message to history\n", "        self.conversation_history.append({\n", "            \"timestamp\": datetime.now().strftime(\"%H:%M:%S\"),\n", "            \"sender\": \"user\",\n", "            \"message\": message\n", "        })\n", "        \n", "        try:\n", "            # Get response using the reflexion coach\n", "            result = await self.reflexion_coach.process_query_with_reflexion(message)\n", "            \n", "            # Extract the final response from the result dictionary\n", "            response = result.get('final_response', 'Sorry, I could not generate a response.')\n", "            \n", "            # Add bot response to history\n", "            self.conversation_history.append({\n", "                \"timestamp\": datetime.now().strftime(\"%H:%M:%S\"),\n", "                \"sender\": \"bot\",\n", "                \"message\": response,\n", "                \"metadata\": {\n", "                    \"quality_score\": result.get('final_score', {}).get('overall', 0),\n", "                    \"iterations\": result.get('iterations', 1),\n", "                    \"improvement\": result.get('improvement', 0)\n", "                }\n", "            })\n", "            \n", "            return response\n", "            \n", "        except Exception as e:\n", "            error_msg = f\"Sorry, I encountered an error: {str(e)}\"\n", "            self.conversation_history.append({\n", "                \"timestamp\": datetime.now().strftime(\"%H:%M:%S\"),\n", "                \"sender\": \"bot\",\n", "                \"message\": error_msg\n", "            })\n", "            return error_msg\n", "    \n", "    def deactivate(self):\n", "        \"\"\"Deactivate the chat session\"\"\"\n", "        self.is_active = False\n", "        print(f\"🔴 Session {self.session_id} deactivated.\")\n", "        print(f\"📊 Session Summary:\")\n", "        print(f\"   - User: {self.user_id}\")\n", "        print(f\"   - Coach: {self.reflexion_coach.coach.name}\")\n", "        print(f\"   - Duration: {datetime.now() - self.created_at}\")\n", "        print(f\"   - Messages exchanged: {len(self.conversation_history)}\")\n", "        \n", "        # Show quality metrics\n", "        bot_messages = [msg for msg in self.conversation_history if msg[\"sender\"] == \"bot\" and \"metadata\" in msg]\n", "        if bot_messages:\n", "            avg_quality = sum(msg[\"metadata\"][\"quality_score\"] for msg in bot_messages) / len(bot_messages)\n", "            total_iterations = sum(msg[\"metadata\"][\"iterations\"] for msg in bot_messages)\n", "            print(f\"   - Average response quality: {avg_quality:.1f}/10\")\n", "            print(f\"   - Total reflexion iterations: {total_iterations}\")\n", "    \n", "    def get_session_info(self) -> str:\n", "        \"\"\"Get current session information\"\"\"\n", "        status = \"🟢 Active\" if self.is_active else \"🔴 Inactive\"\n", "        return f\"\"\"\n", "📱 Chat Session Info:\n", "   Session ID: {self.session_id}\n", "   User ID: {self.user_id}\n", "   Coach Name: {self.reflexion_coach.coach.name}\n", "   Coach Specialty: {self.reflexion_coach.coach.specialty}\n", "   Status: {status}\n", "   Started: {self.created_at.strftime(\"%H:%M:%S\")}\n", "   Messages: {len(self.conversation_history)}\n", "        \"\"\"\n", "\n", "async def start_chat_interface(coach_id: str = \"career_assessment\"):\n", "    \"\"\"Start the interactive chat interface\"\"\"\n", "    \n", "    print(\"🚀 Starting Career Coach <PERSON><PERSON> Interface...\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Available coach types\n", "    available_coaches = {\n", "        \"career_assessment\": \"Career Assessment Coach\",\n", "        \"resume_builder\": \"Resume Builder Coach\", \n", "        \"linkedin_optimizer\": \"LinkedIn Optimizer Coach\",\n", "        \"networking_strategy\": \"Networking Strategy Coach\"\n", "    }\n", "    \n", "    print(\"🎭 Available Coaches:\")\n", "    for cid, name in available_coaches.items():\n", "        print(f\"   - {cid}: {name}\")\n", "    \n", "    # Create new session\n", "    try:\n", "        session = ChatSession(coach_id)\n", "        \n", "        print(f\"\\n✅ New session created!\")\n", "        print(session.get_session_info())\n", "        print(\"=\" * 60)\n", "        print(\"💬 You can now chat with the career coach!\")\n", "        print(\"💡 Commands:\")\n", "        print(\"   - 'exit' : End the session\")\n", "        print(\"   - 'info' : Show session information\")\n", "        print(\"   - 'switch <coach_id>' : Switch to different coach\")\n", "        print(\"=\" * 60)\n", "        \n", "        while session.is_active:\n", "            try:\n", "                # Get user input\n", "                user_input = input(f\"\\n{session.user_id}: \").strip()\n", "                \n", "                # Check for exit command\n", "                if user_input.lower() == 'exit':\n", "                    session.deactivate()\n", "                    break\n", "                \n", "                # Check for info command\n", "                if user_input.lower() == 'info':\n", "                    print(session.get_session_info())\n", "                    continue\n", "                \n", "                # Check for switch command\n", "                if user_input.lower().startswith('switch '):\n", "                    new_coach_id = user_input[7:].strip()\n", "                    if new_coach_id in available_coaches:\n", "                        # Create new session with different coach\n", "                        old_session_id = session.session_id\n", "                        session.deactivate()\n", "                        session = ChatSession(new_coach_id)\n", "                        print(f\"🔄 Switched from session {old_session_id} to new coach\")\n", "                        print(session.get_session_info())\n", "                        continue\n", "                    else:\n", "                        print(f\"❌ Unknown coach ID. Available: {list(available_coaches.keys())}\")\n", "                        continue\n", "                \n", "                # Skip empty messages\n", "                if not user_input:\n", "                    continue\n", "                \n", "                # Show typing indicator\n", "                print(\"🤔 Coach is thinking...\")\n", "                \n", "                # Get response from the bot\n", "                response = await session.send_message(user_input)\n", "                \n", "                # Display response\n", "                print(f\"\\n🤖 {session.reflexion_coach.coach.name}: {response}\")\n", "                \n", "            except KeyboardInterrupt:\n", "                print(\"\\n\\n⚠️ Chat interrupted by user\")\n", "                session.deactivate()\n", "                break\n", "            except Exception as e:\n", "                print(f\"\\n❌ Error: {str(e)}\")\n", "                continue\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Failed to create session: {str(e)}\")\n", "        return None\n", "    \n", "    print(\"\\n👋 Thank you for using the Career Coach Chat Interface!\")\n", "    return session\n", "\n", "# Helper function to start a new chat session with specific coach\n", "async def new_chat(coach_id: str = \"career_assessment\"):\n", "    \"\"\"Quick function to start a new chat session\"\"\"\n", "    return await start_chat_interface(coach_id)"]}, {"cell_type": "code", "execution_count": 22, "id": "8aba9513", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Starting Career Coach <PERSON><PERSON>face...\n", "============================================================\n", "🎭 Available Coaches:\n", "   - career_assessment: Career Assessment Coach\n", "   - resume_builder: <PERSON><PERSON> Builder Coach\n", "   - linkedin_optimizer: LinkedIn Optimizer Coach\n", "   - networking_strategy: Networking Strategy Coach\n", "🤖 Initialized Reflexion Career Coach: Career Assessment Coach\n", "📋 Specialty: Career Assessment & Path Planning\n", "🎯 Focus Areas: MBTI personality assessment and career alignment, Strong Interest Inventory for career matching, PRINT assessment for unconscious motivators and stress triggers, Holland Code (RIASEC) test interpretation, Risk propensity evaluation for industry fit, Self-exploration using Columbia University tools, Career path recommendations based on personality and interests, InternUp Industrial Projects matching and recommendations\n", "\n", "✅ New session created!\n", "\n", "📱 Chat Session Info:\n", "   Session ID: dc190931\n", "   User ID: visitor_3796\n", "   Coach Name: Career Assessment Coach\n", "   Coach Specialty: Career Assessment & Path Planning\n", "   Status: 🟢 Active\n", "   Started: 16:57:01\n", "   Messages: 0\n", "        \n", "============================================================\n", "💬 You can now chat with the career coach!\n", "💡 Commands:\n", "   - 'exit' : End the session\n", "   - 'info' : Show session information\n", "   - 'switch <coach_id>' : Switch to different coach\n", "============================================================\n", "🤔 Coach is thinking...\n", "\n", "🔄 Processing query with reflexion pattern...\n", "📝 Query: hi\n", "🎯 Query Type: advice_request\n", "💭 Initial Response (50 words): Hello. I'm <PERSON>, your AI career coach. I'm super excited to help you explore your passions, talent...\n", "\n", "🔍 Reflexion Iteration 1\n", "📊 Reflection Scores:\n", "   Brevity: 4.0/10\n", "   Relevance: 0.6/10\n", "   Actionability: 3.0/10\n", "   Clarity: 10.0/10\n", "   Overall: 3.9/10\n", "🔧 Refining response...\n", "💭 Refined Response (58 words): Hello. I'm <PERSON>, your AI career coach. I'm super excited to help you explore your passions, talents, and strengths to find a career that's just perfect for you. \n", "\n", "Is this your first time using our app, and are you ready to discover your true self and unlock your dream career? Consider taking these specific steps to move forward....\n", "\n", "🔍 Reflexion Iteration 2\n", "📊 Reflection Scores:\n", "   Brevity: 4.0/10\n", "   Relevance: 0.6/10\n", "   Actionability: 6.0/10\n", "   Clarity: 10.0/10\n", "   Overall: 4.7/10\n", "🔧 Refining response...\n", "💭 Refined Response (58 words): Hello. I'm <PERSON>, your AI career coach. I'm super excited to help you explore your passions, talents, and strengths to find a career that's just perfect for you. \n", "\n", "Is this your first time using our app, and are you ready to discover your true self and unlock your dream career? Consider taking these specific steps to move forward....\n", "\n", "🔍 Reflexion Iteration 3\n", "📊 Reflection Scores:\n", "   Brevity: 4.0/10\n", "   Relevance: 0.6/10\n", "   Actionability: 6.0/10\n", "   Clarity: 10.0/10\n", "   Overall: 4.7/10\n", "🔧 Refining response...\n", "💭 Refined Response (58 words): Hello. I'm <PERSON>, your AI career coach. I'm super excited to help you explore your passions, talents, and strengths to find a career that's just perfect for you. \n", "\n", "Is this your first time using our app, and are you ready to discover your true self and unlock your dream career? Consider taking these specific steps to move forward....\n", "\n", "🤖 Career Assessment Coach: Sorry, I encountered an error: 'ReflexionScore' object has no attribute 'get'\n", "🤔 Coach is thinking...\n", "\n", "🔄 Processing query with reflexion pattern...\n", "📝 Query: yes\n", "🎯 Query Type: advice_request\n", "💭 Initial Response (98 words): I'm so excited to start this coaching session with you. My name is <PERSON>, and I'll be your AI caree...\n", "\n", "🔍 Reflexion Iteration 1\n", "📊 Reflection Scores:\n", "   Brevity: 8.0/10\n", "   Relevance: 0.6/10\n", "   Actionability: 10.0/10\n", "   Clarity: 10.0/10\n", "   Overall: 6.7/10\n", "🔧 Refining response...\n", "💭 Refined Response (98 words): I'm so excited to start this coaching session with you. My name is <PERSON>, and I'll be your AI career coach throughout this journey. I'll help you discover your passions, talents, and strengths to find the perfect career fit for you.\n", "\n", "Before we begin, I just want to confirm that this is your first time using our app, right? If so, don't worry, I'll guide you through every step of the way. We'll have some fun assessments and conversations to help you get to know yourself better.\n", "\n", "So, to get started, can you please tell me your name?...\n", "\n", "🔍 Reflexion Iteration 2\n", "📊 Reflection Scores:\n", "   Brevity: 8.0/10\n", "   Relevance: 0.6/10\n", "   Actionability: 10.0/10\n", "   Clarity: 10.0/10\n", "   Overall: 6.7/10\n", "🔧 Refining response...\n", "💭 Refined Response (98 words): I'm so excited to start this coaching session with you. My name is <PERSON>, and I'll be your AI career coach throughout this journey. I'll help you discover your passions, talents, and strengths to find the perfect career fit for you.\n", "\n", "Before we begin, I just want to confirm that this is your first time using our app, right? If so, don't worry, I'll guide you through every step of the way. We'll have some fun assessments and conversations to help you get to know yourself better.\n", "\n", "So, to get started, can you please tell me your name?...\n", "\n", "🔍 Reflexion Iteration 3\n", "📊 Reflection Scores:\n", "   Brevity: 8.0/10\n", "   Relevance: 0.6/10\n", "   Actionability: 10.0/10\n", "   Clarity: 10.0/10\n", "   Overall: 6.7/10\n", "🔧 Refining response...\n", "💭 Refined Response (98 words): I'm so excited to start this coaching session with you. My name is <PERSON>, and I'll be your AI career coach throughout this journey. I'll help you discover your passions, talents, and strengths to find the perfect career fit for you.\n", "\n", "Before we begin, I just want to confirm that this is your first time using our app, right? If so, don't worry, I'll guide you through every step of the way. We'll have some fun assessments and conversations to help you get to know yourself better.\n", "\n", "So, to get started, can you please tell me your name?...\n", "\n", "🤖 Career Assessment Coach: Sorry, I encountered an error: 'ReflexionScore' object has no attribute 'get'\n", "🤔 Coach is thinking...\n", "\n", "🔄 Processing query with reflexion pattern...\n", "📝 Query: hars<PERSON><PERSON>\n", "🎯 Query Type: advice_request\n", "💭 Initial Response (65 words): Hello <PERSON><PERSON><PERSON><PERSON>, I'm <PERSON>, your AI career coach. I'm thrilled to have you on board and excited to ...\n", "\n", "🔍 Reflexion Iteration 1\n", "📊 Reflection Scores:\n", "   Brevity: 6.0/10\n", "   Relevance: 0.6/10\n", "   Actionability: 3.0/10\n", "   Clarity: 10.0/10\n", "   Overall: 4.4/10\n", "🔧 Refining response...\n", "💭 Refined Response (73 words): Hello <PERSON><PERSON><PERSON><PERSON>, I'm <PERSON>, your AI career coach. I'm thrilled to have you on board and excited to help you discover your passions and strengths. You're taking the first step towards unlocking your full potential, and I'm honored to be a part of your journey.\n", "\n", "Before we dive in, I just want to confirm - is this your first time using our career exploration app? Consider taking these specific steps to move forward....\n", "\n", "🔍 Reflexion Iteration 2\n", "📊 Reflection Scores:\n", "   Brevity: 6.0/10\n", "   Relevance: 0.6/10\n", "   Actionability: 6.0/10\n", "   Clarity: 10.0/10\n", "   Overall: 5.2/10\n", "🔧 Refining response...\n", "💭 Refined Response (73 words): Hello <PERSON><PERSON><PERSON><PERSON>, I'm <PERSON>, your AI career coach. I'm thrilled to have you on board and excited to help you discover your passions and strengths. You're taking the first step towards unlocking your full potential, and I'm honored to be a part of your journey.\n", "\n", "Before we dive in, I just want to confirm - is this your first time using our career exploration app? Consider taking these specific steps to move forward....\n", "\n", "🔍 Reflexion Iteration 3\n", "📊 Reflection Scores:\n", "   Brevity: 6.0/10\n", "   Relevance: 0.6/10\n", "   Actionability: 6.0/10\n", "   Clarity: 10.0/10\n", "   Overall: 5.2/10\n", "🔧 Refining response...\n", "💭 Refined Response (73 words): Hello <PERSON><PERSON><PERSON><PERSON>, I'm <PERSON>, your AI career coach. I'm thrilled to have you on board and excited to help you discover your passions and strengths. You're taking the first step towards unlocking your full potential, and I'm honored to be a part of your journey.\n", "\n", "Before we dive in, I just want to confirm - is this your first time using our career exploration app? Consider taking these specific steps to move forward....\n", "\n", "🤖 Career Assessment Coach: Sorry, I encountered an error: 'ReflexionScore' object has no attribute 'get'\n", "🔴 Session dc190931 deactivated.\n", "📊 Session Summary:\n", "   - User: visitor_3796\n", "   - Coach: Career Assessment Coach\n", "   - Duration: 0:27:18.502268\n", "   - Messages exchanged: 6\n", "\n", "👋 Thank you for using the Career Coach Cha<PERSON> Interface!\n"]}, {"data": {"text/plain": ["<__main__.ChatSession at 0x7f6cc0084e50>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# Start the chat interface\n", "# Available coach types: career_assessment, resume_builder, linkedin_optimizer, networking_strategy\n", "await start_chat_interface(\"career_assessment\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}