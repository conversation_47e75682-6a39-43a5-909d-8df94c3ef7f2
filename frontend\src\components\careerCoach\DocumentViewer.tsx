import { useState } from "react"
import { Download, File, FileText, Image, ZoomIn, ZoomOut } from "lucide-react"

import { Button } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface DocumentViewerProps {
  coachId: string
  coachSpecialty: string
}

const mockDocuments = {
  "resume-builder": {
    type: "pdf",
    title: "Your Resume",
    url: "/placeholder-resume.pdf",
    description: "Current version of your resume for optimization",
  },
  "linkedin-optimizer": {
    type: "image",
    title: "LinkedIn Profile Screenshot",
    url: "/placeholder-linkedin.png",
    description: "Screenshot of your current LinkedIn profile",
  },
  "career-assessment": {
    type: "template",
    title: "Career Assessment Template",
    url: null,
    description: "Interactive career planning worksheet",
  },
}

export default function DocumentViewer({
  coachId,
  coachSpecialty,
}: DocumentViewerProps) {
  const [zoom, setZoom] = useState(100)

  const document =
    mockDocuments[coachId as keyof typeof mockDocuments] ||
    mockDocuments["career-assessment"]

  const handleZoomIn = () => setZoom((prev) => Math.min(prev + 25, 200))
  const handleZoomOut = () => setZoom((prev) => Math.max(prev - 25, 50))

  if (document.type === "template") {
    return (
      <Card className="h-full">
        <CardHeader className="border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg">{document.title}</CardTitle>
            </div>
          </div>
          <p className="text-sm text-gray-600">{document.description}</p>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900">Career Goals</h3>
              <div className="space-y-2">
                <div className="rounded-lg border bg-gray-50 p-3">
                  <p className="text-sm text-gray-600">
                    Short-term goal (1 year)
                  </p>
                  <div className="mt-1 flex h-8 items-center rounded border bg-white px-3 text-sm text-gray-400">
                    Click to edit...
                  </div>
                </div>
                <div className="rounded-lg border bg-gray-50 p-3">
                  <p className="text-sm text-gray-600">
                    Long-term goal (5 years)
                  </p>
                  <div className="mt-1 flex h-8 items-center rounded border bg-white px-3 text-sm text-gray-400">
                    Click to edit...
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900">Skills Assessment</h3>
              <div className="grid grid-cols-2 gap-3">
                {[
                  "Leadership",
                  "Communication",
                  "Technical Skills",
                  "Problem Solving",
                ].map((skill) => (
                  <div key={skill} className="rounded-lg border bg-gray-50 p-3">
                    <p className="text-sm font-medium text-gray-700">{skill}</p>
                    <div className="mt-2 flex gap-1">
                      {[1, 2, 3, 4, 5].map((i) => (
                        <div
                          key={i}
                          className="h-4 w-4 cursor-pointer rounded-full bg-gray-200 hover:bg-blue-200"
                        ></div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="h-full">
      <CardHeader className="border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {document.type === "pdf" ? (
              <File className="size-5 text-red-600" />
            ) : (
              <Image className="size-5 text-green-600" />
            )}
            <CardTitle className="text-lg">{document.title}</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleZoomOut}>
              <ZoomOut className="size-4" />
            </Button>
            <span className="min-w-12 text-center text-sm text-gray-600">
              {zoom}%
            </span>
            <Button variant="outline" size="sm" onClick={handleZoomIn}>
              <ZoomIn className="size-4" />
            </Button>
            <Button variant="outline" size="sm">
              <Download className="size-4" />
            </Button>
          </div>
        </div>
        <p className="text-sm text-gray-600">{document.description}</p>
      </CardHeader>
      <CardContent className="h-full p-0">
        <div className="flex h-full items-center justify-center bg-gray-50">
          {document.type === "pdf" ? (
            <div className="text-center">
              <File className="mx-auto mb-4 size-16 text-gray-400" />
              <p className="text-gray-500">PDF viewer placeholder</p>
              <p className="mt-1 text-sm text-gray-400">
                Your resume will be displayed here
              </p>
            </div>
          ) : (
            <div className="text-center">
              <Image className="mx-auto mb-4 size-16 text-gray-400" />
              <p className="text-gray-500">Image viewer placeholder</p>
              <p className="mt-1 text-sm text-gray-400">
                Your LinkedIn screenshot will be displayed here
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
