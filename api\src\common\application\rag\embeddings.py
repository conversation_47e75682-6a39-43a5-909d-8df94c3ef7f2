from langchain_huggingface import Hugging<PERSON>aceEmbeddings
from typing import Dict, <PERSON><PERSON>
from loguru import logger

EmbeddingsModel = HuggingFaceEmbeddings

# Global cache for embedding models
_embedding_model_cache: Dict[Tuple[str, str], HuggingFaceEmbeddings] = {}


def get_embedding_model(
    model_id: str,
    device: str = "cpu",
) -> EmbeddingsModel:
    """Gets an instance of a HuggingFace embedding model with caching.

    Args:
        model_id (str): The ID/name of the HuggingFace embedding model to use
        device (str): The compute device to run the model on (e.g. "cpu", "cuda").
            Defaults to "cpu"

    Returns:
        EmbeddingsModel: A configured HuggingFace embeddings model instance
    """
    cache_key = (model_id, device)

    if cache_key in _embedding_model_cache:
        logger.info(f"Using cached embedding model: {model_id} on {device}")
        return _embedding_model_cache[cache_key]

    logger.info(f"Loading new embedding model: {model_id} on {device}")
    model = get_huggingface_embedding_model(model_id, device)
    _embedding_model_cache[cache_key] = model

    return model


def get_huggingface_embedding_model(
    model_id: str, device: str
) -> HuggingFaceEmbeddings:
    """Gets a HuggingFace embedding model instance.

    Args:
        model_id (str): The ID/name of the HuggingFace embedding model to use
        device (str): The compute device to run the model on (e.g. "cpu", "cuda")

    Returns:
        HuggingFaceEmbeddings: A configured HuggingFace embeddings model instance
            with remote code trust enabled and embedding normalization disabled
    """
    return HuggingFaceEmbeddings(
        model_name=model_id,
        model_kwargs={"device": device, "trust_remote_code": True},
        encode_kwargs={"normalize_embeddings": False},
    )
